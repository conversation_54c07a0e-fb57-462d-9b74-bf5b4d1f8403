class_name GenericGameUI
extends BaseGameUI

# 通用游戏UI组件
@onready var game_info_container: VBoxContainer = $MainContainer/GameInfoContainer
@onready var game_title_label: Label = $MainContainer/GameInfoContainer/GameTitleLabel
@onready var game_rules_label: Label = $MainContainer/GameInfoContainer/GameRulesLabel
@onready var action_container: HBoxContainer = $MainContainer/ActionContainer
@onready var new_game_button: Button = $MainContainer/ActionContainer/NewGameButton
@onready var action_button: Button = $MainContainer/ActionContainer/ActionButton

func _ready():
	super._ready()
	
	# 设置组件引用
	game_status_label = $MainContainer/GameStatusLabel
	main_container = $MainContainer
	button_container = $MainContainer/ActionContainer

func initialize_ui():
	# 连接按钮信号
	if new_game_button:
		new_game_button.pressed.connect(_on_new_game_button_pressed)
	if action_button:
		action_button.pressed.connect(_on_action_button_pressed)
	
	# 初始化状态
	update_status("选择游戏开始")
	update_ui_state()

func set_game_instance(game: BaseGame):
	super.set_game_instance(game)
	
	# 更新游戏信息显示
	if game_instance:
		update_game_info()

func update_game_info():
	if not game_instance:
		return
	
	# 更新游戏标题
	if game_title_label:
		game_title_label.text = game_instance.get_game_name()
	
	# 更新游戏规则
	if game_rules_label:
		game_rules_label.text = game_instance.get_game_rules()

func _on_new_game_button_pressed():
	if game_instance:
		game_instance.start_new_game()

func _on_action_button_pressed():
	if not game_instance:
		return
	
	# 根据游戏类型执行不同的动作
	if game_instance is WarGame:
		var war_game = game_instance as WarGame
		war_game.player_flip_card()
	elif game_instance is MemoryGame:
		# 记忆翻牌游戏需要特殊处理
		show_message("点击卡牌进行翻牌", 2.0)
	elif game_instance is GoFishGame:
		# 钓鱼游戏需要特殊处理
		show_message("选择要询问的牌面", 2.0)
	elif game_instance is CrazyEightsGame:
		var crazy_game = game_instance as CrazyEightsGame
		crazy_game.player_draw_card()
	elif game_instance is OldMaidGame:
		# 抽乌龟游戏需要特殊处理
		show_message("选择要抽牌的玩家", 2.0)
	else:
		show_message("该游戏需要专用UI", 2.0)

func update_ui_state():
	if not game_instance:
		return
	
	var state = game_instance.get_current_state()
	
	# 更新按钮状态
	if new_game_button:
		new_game_button.disabled = (state == BaseGame.GameState.DEALING)
	
	if action_button:
		action_button.disabled = (state != BaseGame.GameState.PLAYING)
		
		# 根据游戏类型更新按钮文本
		if game_instance is WarGame:
			action_button.text = "翻牌"
		elif game_instance is CrazyEightsGame:
			action_button.text = "抽牌"
		else:
			action_button.text = "动作"

func _on_game_started():
	super._on_game_started()
	update_ui_state()

func _on_game_ended(result: Dictionary):
	super._on_game_ended(result)
	update_ui_state()

func refresh_ui():
	update_ui_state()
	update_game_info()

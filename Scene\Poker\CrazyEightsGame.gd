class_name CrazyE<PERSON>s<PERSON>ame
extends BaseGame

# 疯狂八特有信号
signal card_played(player_name: String, card: PokerCard)
signal suit_changed(new_suit: PokerCard.Suit)
signal card_drawn(player_name: String, card: PokerCard)
signal player_uno_called(player_name: String)

# 游戏状态
var player_hands: Array[Array] = [[], []]  # 2个玩家的手牌
var discard_pile: Array = []               # 弃牌堆
var draw_pile: Array = []                  # 抽牌堆
var current_suit: PokerCard.Suit           # 当前花色
var current_rank: PokerCard.Rank           # 当前牌面
var current_turn: int = 0                  # 当前回合玩家

func _init():
	super._init()
	# 添加玩家和AI
	add_player("玩家")
	add_player("AI")

# 获取游戏名称
func get_game_name() -> String:
	return "疯狂八"

# 获取游戏规则
func get_game_rules() -> String:
	return "出完手牌获胜。必须出相同花色或相同点数的牌。8可以改变花色。剩1张牌时要喊UNO。"

# 开始新游戏
func start_new_game():
	super.start_new_game()
	
	# 重置游戏状态
	reset_game_state()
	
	# 发牌
	deal_initial_cards()
	
	# 翻开第一张牌
	start_discard_pile()
	
	# 开始游戏
	current_turn = 0
	set_state(GameState.PLAYING)
	turn_changed.emit("玩家")

# 重置游戏状态
func reset_game_state():
	for i in range(2):
		player_hands[i].clear()
		players[i].hand.clear()
	
	discard_pile.clear()
	draw_pile.clear()
	current_turn = 0

# 发初始牌（每人7张）
func deal_initial_cards():
	check_deck_size(52)
	
	# 每人发7张牌
	for round in range(7):
		for player_index in range(2):
			var card = deal_card()
			card.is_face_up = (player_index == 0)  # 只有玩家的牌正面朝上
			player_hands[player_index].append(card)
			players[player_index].hand.append(card)
			card_dealt.emit(card, players[player_index].name)
	
	# 剩余牌放入抽牌堆
	draw_pile = deck.duplicate()
	
	# 对玩家手牌排序
	sort_hand(player_hands[0])

# 对手牌排序
func sort_hand(hand: Array):
	hand.sort_custom(func(a, b): 
		if a.suit != b.suit:
			return a.suit < b.suit
		else:
			return a.rank < b.rank
	)

# 开始弃牌堆
func start_discard_pile():
	# 翻开第一张牌作为起始牌
	var first_card = draw_pile.pop_back()
	first_card.is_face_up = true
	discard_pile.append(first_card)
	
	current_suit = first_card.suit
	current_rank = first_card.rank
	
	card_dealt.emit(first_card, "弃牌堆")
	
	# 如果第一张是8，随机选择花色
	if current_rank == PokerCard.Rank.EIGHT:
		current_suit = PokerCard.Suit.values()[randi() % 4]
		suit_changed.emit(current_suit)

# 玩家出牌
func player_play_card(selected_card: PokerCard) -> bool:
	if current_turn != 0 or current_state != GameState.PLAYING:
		return false
	
	# 验证出牌是否有效
	if not is_valid_play(selected_card):
		return false
	
	# 从手牌中移除
	player_hands[0].erase(selected_card)
	players[0].hand.erase(selected_card)
	
	# 添加到弃牌堆
	selected_card.is_face_up = true
	discard_pile.append(selected_card)
	
	# 更新当前状态
	current_rank = selected_card.rank
	if selected_card.rank != PokerCard.Rank.EIGHT:
		current_suit = selected_card.suit
	
	card_played.emit("玩家", selected_card)
	
	# 如果出的是8，让玩家选择花色
	if selected_card.rank == PokerCard.Rank.EIGHT:
		# 简化：自动选择玩家手中最多的花色
		var new_suit = choose_best_suit_for_player()
		current_suit = new_suit
		suit_changed.emit(current_suit)
	
	# 检查UNO
	if player_hands[0].size() == 1:
		player_uno_called.emit("玩家")
	
	# 检查是否获胜
	if player_hands[0].is_empty():
		end_game({"winner": "玩家", "reason": "出完所有牌"})
		return true
	
	# 轮到AI
	current_turn = 1
	ai_turn()
	return true

# 玩家选择花色（当出8时）
func player_choose_suit(suit: PokerCard.Suit):
	current_suit = suit
	suit_changed.emit(current_suit)

# 为玩家选择最佳花色
func choose_best_suit_for_player() -> PokerCard.Suit:
	var suit_counts = {
		PokerCard.Suit.HEARTS: 0,
		PokerCard.Suit.DIAMONDS: 0,
		PokerCard.Suit.CLUBS: 0,
		PokerCard.Suit.SPADES: 0
	}
	
	# 统计玩家手中各花色的数量
	for card in player_hands[0]:
		if card.rank != PokerCard.Rank.EIGHT:  # 不计算8
			suit_counts[card.suit] += 1
	
	# 选择数量最多的花色
	var best_suit = PokerCard.Suit.HEARTS
	var max_count = 0
	
	for suit in suit_counts:
		if suit_counts[suit] > max_count:
			max_count = suit_counts[suit]
			best_suit = suit
	
	return best_suit

# 验证出牌是否有效
func is_valid_play(card: PokerCard) -> bool:
	# 8可以出任何时候
	if card.rank == PokerCard.Rank.EIGHT:
		return true
	
	# 相同花色或相同点数
	return card.suit == current_suit or card.rank == current_rank

# 玩家抽牌
func player_draw_card() -> bool:
	if current_turn != 0 or current_state != GameState.PLAYING:
		return false
	
	# 检查是否有可出的牌
	if has_playable_card(0):
		return false  # 有可出的牌，不能抽牌
	
	# 抽牌
	var card = draw_card_from_pile()
	if card:
		card.is_face_up = true
		player_hands[0].append(card)
		players[0].hand.append(card)
		
		card_drawn.emit("玩家", card)
		sort_hand(player_hands[0])
		
		# 如果抽到的牌可以出，玩家可以选择出牌或结束回合
		if is_valid_play(card):
			# 玩家可以选择出牌
			return true
		else:
			# 轮到AI
			current_turn = 1
			ai_turn()
	
	return true

# 检查是否有可出的牌
func has_playable_card(player_index: int) -> bool:
	for card in player_hands[player_index]:
		if is_valid_play(card):
			return true
	return false

# 从抽牌堆抽牌
func draw_card_from_pile() -> PokerCard:
	if draw_pile.is_empty():
		# 重新洗牌（除了最后一张弃牌）
		if discard_pile.size() <= 1:
			return null  # 没有牌了
		
		var last_card = discard_pile.pop_back()
		
		# 将弃牌堆重新洗入抽牌堆
		for card in discard_pile:
			card.is_face_up = false
			draw_pile.append(card)
		
		discard_pile.clear()
		discard_pile.append(last_card)
		
		# 洗牌
		shuffle_array(draw_pile)
	
	return draw_pile.pop_back()

# 洗牌数组
func shuffle_array(array: Array):
	for i in range(array.size()):
		var j = randi() % array.size()
		var temp = array[i]
		array[i] = array[j]
		array[j] = temp

# AI回合
func ai_turn():
	if current_state != GameState.PLAYING:
		return
	
	var ai_hand = player_hands[1]
	
	# 检查是否有可出的牌
	var playable_cards = []
	for card in ai_hand:
		if is_valid_play(card):
			playable_cards.append(card)
	
	if not playable_cards.is_empty():
		# 出牌
		var card_to_play = playable_cards[0]  # 简化：出第一张可出的牌
		
		ai_hand.erase(card_to_play)
		players[1].hand.erase(card_to_play)
		
		card_to_play.is_face_up = true
		discard_pile.append(card_to_play)
		
		current_rank = card_to_play.rank
		if card_to_play.rank != PokerCard.Rank.EIGHT:
			current_suit = card_to_play.suit
		
		card_played.emit("AI", card_to_play)
		
		# 如果出的是8，AI选择花色
		if card_to_play.rank == PokerCard.Rank.EIGHT:
			var new_suit = choose_best_suit_for_ai()
			current_suit = new_suit
			suit_changed.emit(current_suit)
		
		# 检查UNO
		if ai_hand.size() == 1:
			player_uno_called.emit("AI")
		
		# 检查是否获胜
		if ai_hand.is_empty():
			end_game({"winner": "AI", "reason": "出完所有牌"})
			return
	else:
		# 抽牌
		var card = draw_card_from_pile()
		if card:
			card.is_face_up = false
			ai_hand.append(card)
			players[1].hand.append(card)
			
			card_drawn.emit("AI", card)
			
			# 如果抽到的牌可以出，AI有50%概率出牌
			if is_valid_play(card) and randi() % 2 == 0:
				# AI选择出牌
				ai_turn()
				return
	
	# 轮到玩家
	current_turn = 0
	turn_changed.emit("玩家")

# AI选择最佳花色
func choose_best_suit_for_ai() -> PokerCard.Suit:
	var suit_counts = {
		PokerCard.Suit.HEARTS: 0,
		PokerCard.Suit.DIAMONDS: 0,
		PokerCard.Suit.CLUBS: 0,
		PokerCard.Suit.SPADES: 0
	}
	
	# 统计AI手中各花色的数量
	for card in player_hands[1]:
		if card.rank != PokerCard.Rank.EIGHT:
			suit_counts[card.suit] += 1
	
	# 选择数量最多的花色
	var best_suit = PokerCard.Suit.HEARTS
	var max_count = 0
	
	for suit in suit_counts:
		if suit_counts[suit] > max_count:
			max_count = suit_counts[suit]
			best_suit = suit
	
	return best_suit

# 获取玩家手牌
func get_player_hand() -> Array:
	return player_hands[0]

# 获取AI手牌数量
func get_ai_hand_count() -> int:
	return player_hands[1].size()

# 获取当前花色
func get_current_suit() -> PokerCard.Suit:
	return current_suit

# 获取当前牌面
func get_current_rank() -> PokerCard.Rank:
	return current_rank

# 获取弃牌堆顶牌
func get_top_discard_card() -> PokerCard:
	if not discard_pile.is_empty():
		return discard_pile[-1]
	return null

# 获取抽牌堆剩余数量
func get_draw_pile_count() -> int:
	return draw_pile.size()

# 获取当前回合玩家
func get_current_turn() -> int:
	return current_turn

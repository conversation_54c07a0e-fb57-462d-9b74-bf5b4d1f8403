class_name Blackjack<PERSON>ame
extends BaseGame

# 21点特有信号
signal player_hit()
signal player_stand()
signal dealer_turn_started()

# 21点游戏状态
enum BlackjackState {
	PLAYER_TURN,
	DEALER_TURN
}

# 游戏设置
var target_score: int = 21
var dealer_hit_limit: int = 17
var blackjack_state: BlackjackState

# 玩家和庄家手牌
var player_hand: Array[PokerCard] = []
var dealer_hand: Array[PokerCard] = []

func _init():
	super._init()
	# 添加玩家和庄家
	add_player("玩家")
	add_player("庄家")

# 获取游戏名称
func get_game_name() -> String:
	return "21点"

# 获取游戏规则
func get_game_rules() -> String:
	return "目标是让手牌总和接近21点但不超过。A可以当1或11，J/Q/K都算10点。"

# 开始新游戏
func start_new_game():
	super.start_new_game()
	
	# 重置游戏状态
	player_hand.clear()
	dealer_hand.clear()
	clear_all_hands()
	
	# 检查牌组
	check_deck_size(10)
	
	set_state(GameState.DEALING)
	
	# 发初始牌
	deal_initial_cards()
	
	# 进入玩家回合
	blackjack_state = BlackjackState.PLAYER_TURN
	set_state(GameState.PLAYING)
	turn_changed.emit("玩家")

# 发初始牌
func deal_initial_cards():
	# 玩家2张，庄家2张（第二张背面）
	deal_card_to_player()
	deal_card_to_dealer(true)   # 庄家第一张正面
	deal_card_to_player()
	deal_card_to_dealer(false)  # 庄家第二张背面

# 给玩家发牌
func deal_card_to_player() -> PokerCard:
	var card = deal_card()
	if card:
		card.is_face_up = true
		player_hand.append(card)
		players[0].hand.append(card)
		card_dealt.emit(card, "玩家")
	return card

# 给庄家发牌
func deal_card_to_dealer(face_up: bool = true) -> PokerCard:
	var card = deal_card()
	if card:
		card.is_face_up = face_up
		dealer_hand.append(card)
		players[1].hand.append(card)
		card_dealt.emit(card, "庄家")
	return card

# 玩家要牌
func player_hit_card():
	if blackjack_state != BlackjackState.PLAYER_TURN or current_state != GameState.PLAYING:
		return
	
	deal_card_to_player()
	player_hit.emit()
	
	# 检查是否爆牌
	if get_player_score() > target_score:
		end_game({"winner": "庄家", "reason": "玩家爆牌"})

# 玩家停牌
func player_stand_cards():
	if blackjack_state != BlackjackState.PLAYER_TURN or current_state != GameState.PLAYING:
		return
	
	player_stand.emit()
	
	# 切换到庄家回合
	blackjack_state = BlackjackState.DEALER_TURN
	turn_changed.emit("庄家")
	dealer_turn_started.emit()
	
	# 翻开庄家的背面牌
	for card in dealer_hand:
		card.is_face_up = true
	
	# 庄家自动要牌
	dealer_play()

# 庄家自动游戏
func dealer_play():
	while get_dealer_score() < dealer_hit_limit:
		deal_card_to_dealer(true)
	
	# 比较结果
	var player_score = get_player_score()
	var dealer_score = get_dealer_score()
	
	var result = {}
	if dealer_score > target_score:
		result = {"winner": "玩家", "reason": "庄家爆牌"}
	elif player_score > dealer_score:
		result = {"winner": "玩家", "reason": "点数更高"}
	elif dealer_score > player_score:
		result = {"winner": "庄家", "reason": "点数更高"}
	else:
		result = {"winner": "平局", "reason": "点数相同"}
	
	result["player_score"] = player_score
	result["dealer_score"] = dealer_score
	
	end_game(result)

# 计算手牌价值
func calculate_hand_value(hand: Array[PokerCard]) -> int:
	var total = 0
	var aces = 0
	
	for card in hand:
		if card.rank == PokerCard.Rank.ACE:
			aces += 1
			total += 11  # 先按11计算
		elif card.rank >= PokerCard.Rank.JACK:
			total += 10  # J, Q, K都算10
		else:
			total += card.rank
	
	# 处理A的特殊情况（1或11）
	while total > target_score and aces > 0:
		total -= 10  # 将A从11改为1
		aces -= 1
	
	return total

# 获取玩家分数
func get_player_score() -> int:
	return calculate_hand_value(player_hand)

# 获取庄家分数
func get_dealer_score() -> int:
	return calculate_hand_value(dealer_hand)

# 获取庄家可见分数（只计算正面朝上的牌）
func get_dealer_visible_score() -> int:
	var visible_cards: Array[PokerCard] = []
	for card in dealer_hand:
		if card.is_face_up:
			visible_cards.append(card)
	return calculate_hand_value(visible_cards)

# 获取玩家手牌
func get_player_hand() -> Array[PokerCard]:
	return player_hand

# 获取庄家手牌
func get_dealer_hand() -> Array[PokerCard]:
	return dealer_hand

# 检查玩家是否可以要牌
func can_player_hit() -> bool:
	return (blackjack_state == BlackjackState.PLAYER_TURN and 
			current_state == GameState.PLAYING and 
			get_player_score() < target_score)

# 检查玩家是否可以停牌
func can_player_stand() -> bool:
	return (blackjack_state == BlackjackState.PLAYER_TURN and 
			current_state == GameState.PLAYING)

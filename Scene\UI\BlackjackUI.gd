class_name BlackjackUI
extends BaseGameUI

# 21点UI组件
@onready var player_hand_container: HBoxContainer = $MainContainer/PlayerArea/PlayerHandContainer
@onready var dealer_hand_container: HBoxContainer = $MainContainer/DealerArea/DealerHandContainer
@onready var player_score_label: Label = $MainContainer/PlayerArea/PlayerScoreLabel
@onready var dealer_score_label: Label = $MainContainer/DealerArea/DealerScoreLabel
@onready var hit_button: Button = $MainContainer/CenterArea/ButtonContainer/HitButton
@onready var stand_button: Button = $MainContainer/CenterArea/ButtonContainer/StandButton
@onready var new_game_button: Button = $MainContainer/CenterArea/ButtonContainer/NewGameButton

# 卡牌UI数组
var player_card_uis: Array[PokerCardUI] = []
var dealer_card_uis: Array[PokerCardUI] = []

func _ready():
	super._ready()
	
	# 设置组件引用
	game_status_label = $MainContainer/CenterArea/GameStatusLabel
	main_container = $MainContainer
	button_container = $MainContainer/CenterArea/ButtonContainer

func initialize_ui():
	# 连接按钮信号
	if hit_button:
		hit_button.pressed.connect(_on_hit_button_pressed)
	if stand_button:
		stand_button.pressed.connect(_on_stand_button_pressed)
	if new_game_button:
		new_game_button.pressed.connect(_on_new_game_button_pressed)
	
	# 初始化状态
	update_status("点击新游戏开始")
	update_ui_state()

func connect_game_signals():
	super.connect_game_signals()
	
	# 连接21点特有信号
	if game_instance is BlackjackGame:
		var blackjack_game = game_instance as BlackjackGame
		blackjack_game.player_hit.connect(_on_player_hit)
		blackjack_game.player_stand.connect(_on_player_stand)
		blackjack_game.dealer_turn_started.connect(_on_dealer_turn_started)

func _on_card_dealt(card: PokerCard, target: String):
	var card_ui = create_card_ui(card)
	
	if target == "玩家":
		add_card_ui_to_container(card_ui, player_hand_container)
		player_card_uis.append(card_ui)
		play_deal_animation(card_ui, player_card_uis.size() * 0.2)
	elif target == "庄家":
		add_card_ui_to_container(card_ui, dealer_hand_container)
		dealer_card_uis.append(card_ui)
		play_deal_animation(card_ui, dealer_card_uis.size() * 0.2)
	
	# 延迟更新分数
	await get_tree().create_timer(0.3).timeout
	update_scores()

func _on_turn_changed(current_player: String):
	super._on_turn_changed(current_player)
	
	if current_player == "庄家":
		# 翻开庄家的背面牌
		flip_dealer_hidden_cards()
	
	update_ui_state()

func _on_game_ended(result: Dictionary):
	super._on_game_ended(result)
	
	var message = "游戏结束！"
	if "winner" in result:
		message += " 获胜者：" + str(result.winner)
	if "reason" in result:
		message += " (" + str(result.reason) + ")"
	if "player_score" in result and "dealer_score" in result:
		message += "\n玩家：%d 庄家：%d" % [result.player_score, result.dealer_score]
	
	update_status(message)
	update_ui_state()

func _on_player_hit():
	update_ui_state()

func _on_player_stand():
	update_ui_state()

func _on_dealer_turn_started():
	# 处理庄家回合的延迟发牌
	_handle_dealer_turn()

func _on_hit_button_pressed():
	if game_instance is BlackjackGame:
		var blackjack_game = game_instance as BlackjackGame
		blackjack_game.player_hit_card()

func _on_stand_button_pressed():
	if game_instance is BlackjackGame:
		var blackjack_game = game_instance as BlackjackGame
		blackjack_game.player_stand_cards()

func _on_new_game_button_pressed():
	if game_instance:
		game_instance.start_new_game()

# 更新UI状态
func update_ui_state():
	if not game_instance is BlackjackGame:
		return
	
	var blackjack_game = game_instance as BlackjackGame
	var state = blackjack_game.get_current_state()
	
	# 更新按钮状态
	if hit_button:
		hit_button.disabled = not blackjack_game.can_player_hit()
	if stand_button:
		stand_button.disabled = not blackjack_game.can_player_stand()
	if new_game_button:
		new_game_button.disabled = (state == BaseGame.GameState.DEALING)
	
	update_scores()

# 更新分数显示
func update_scores():
	if not game_instance is BlackjackGame:
		return
	
	var blackjack_game = game_instance as BlackjackGame
	
	if player_score_label:
		player_score_label.text = "玩家: %d" % blackjack_game.get_player_score()
	
	if dealer_score_label:
		var dealer_score = blackjack_game.get_dealer_visible_score()
		if blackjack_game.get_current_state() == BaseGame.GameState.GAME_OVER:
			dealer_score = blackjack_game.get_dealer_score()
		dealer_score_label.text = "庄家: %d" % dealer_score

# 翻开庄家的隐藏牌
func flip_dealer_hidden_cards():
	if not game_instance is BlackjackGame:
		return
	
	var blackjack_game = game_instance as BlackjackGame
	var dealer_hand = blackjack_game.get_dealer_hand()
	
	for i in range(dealer_card_uis.size()):
		if i < dealer_hand.size():
			var card = dealer_hand[i]
			var card_ui = dealer_card_uis[i]
			if not card.is_face_up:
				card_ui.flip_card()

# 清空手牌显示
func clear_all_cards():
	super.clear_all_cards()
	
	# 清空玩家手牌
	for card_ui in player_card_uis:
		if is_instance_valid(card_ui):
			card_ui.queue_free()
	player_card_uis.clear()
	
	# 清空庄家手牌
	for card_ui in dealer_card_uis:
		if is_instance_valid(card_ui):
			card_ui.queue_free()
	dealer_card_uis.clear()

# 处理庄家回合的延迟发牌
func _handle_dealer_turn():
	if not game_instance is BlackjackGame:
		return
	
	var blackjack_game = game_instance as BlackjackGame
	if blackjack_game.get_current_state() != BaseGame.GameState.PLAYING:
		return
	
	# 添加延迟让玩家看到庄家的发牌过程
	await get_tree().create_timer(1.0).timeout
	update_scores()

func refresh_ui():
	update_ui_state()
	update_scores()

class_name PokerCard
extends RefCounted

# 扑克牌花色枚举
enum Suit {
	CLUBS,    # 梅花 (C)
	DIAMONDS, # 方块 (D) 
	HEARTS,   # 红桃 (H)
	SPADES    # 黑桃 (P)
}

# 扑克牌牌面枚举
enum Rank {
	ACE = 1,   # A
	TWO = 2,   # 2
	THREE = 3, # 3
	FOUR = 4,  # 4
	FIVE = 5,  # 5
	SIX = 6,   # 6
	SEVEN = 7, # 7
	EIGHT = 8, # 8
	NINE = 9,  # 9
	TEN = 10,  # 10
	JACK = 11, # J
	QUEEN = 12,# Q
	KING = 13, # K
	JOKER = 14 # 大小王
}

var suit: Suit
var rank: Rank
var is_face_up: bool = false

func _init(card_suit: Suit = Suit.CLUBS, card_rank: Rank = Rank.ACE):
	suit = card_suit
	rank = card_rank

# 获取扑克牌的图片路径
func get_texture_path() -> String:
	var theme = "light"  # 使用light主题
	
	if rank == Rank.JOKER:
		return "res://Assets/poker/%s/JOKER.png" % theme
	
	var rank_str = get_rank_string()
	var suit_str = get_suit_string()
	
	return "res://Assets/poker/%s/%s-%s.png" % [theme, rank_str, suit_str]

# 获取牌背图片路径
func get_back_texture_path() -> String:
	return "res://Assets/poker/light/BACK.png"

# 获取牌面字符串
func get_rank_string() -> String:
	match rank:
		Rank.ACE:
			return "A"
		Rank.JACK:
			return "J"
		Rank.QUEEN:
			return "Q"
		Rank.KING:
			return "K"
		Rank.JOKER:
			return "JOKER"
		_:
			return str(rank)

# 获取花色字符串
func get_suit_string() -> String:
	match suit:
		Suit.CLUBS:
			return "C"
		Suit.DIAMONDS:
			return "D"
		Suit.HEARTS:
			return "H"
		Suit.SPADES:
			return "P"
		_:
			return "C"

# 获取扑克牌的显示名称
func get_display_name() -> String:
	if rank == Rank.JOKER:
		return "大王"
	
	var rank_name = ""
	match rank:
		Rank.ACE:
			rank_name = "A"
		Rank.JACK:
			rank_name = "J"
		Rank.QUEEN:
			rank_name = "Q"
		Rank.KING:
			rank_name = "K"
		_:
			rank_name = str(rank)
	
	var suit_name = ""
	match suit:
		Suit.CLUBS:
			suit_name = "♣"
		Suit.DIAMONDS:
			suit_name = "♦"
		Suit.HEARTS:
			suit_name = "♥"
		Suit.SPADES:
			suit_name = "♠"
	
	return suit_name + rank_name

# 获取扑克牌的数值（用于比较大小）
func get_value() -> int:
	if rank == Rank.ACE:
		return 1  # A可以当1或14，这里先当1
	elif rank == Rank.JOKER:
		return 15  # 大王最大
	else:
		return rank

# 比较两张牌的大小
func compare_to(other_card: PokerCard) -> int:
	var my_value = get_value()
	var other_value = other_card.get_value()
	
	if my_value > other_value:
		return 1
	elif my_value < other_value:
		return -1
	else:
		return 0

# 检查是否为红色牌
func is_red() -> bool:
	return suit == Suit.HEARTS or suit == Suit.DIAMONDS

# 检查是否为黑色牌
func is_black() -> bool:
	return suit == Suit.CLUBS or suit == Suit.SPADES

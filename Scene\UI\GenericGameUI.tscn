[gd_scene load_steps=2 format=3 uid="uid://generic_game_ui"]

[ext_resource type="Script" path="res://Scene/UI/GenericGameUI.gd" id="1_generic_ui"]

[node name="GenericGameUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_generic_ui")

[node name="MainContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme_override_constants/separation = 5

[node name="GameInfoContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 2

[node name="GameTitleLabel" type="Label" parent="MainContainer/GameInfoContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 10
text = "游戏名称"
horizontal_alignment = 1

[node name="GameRulesLabel" type="Label" parent="MainContainer/GameInfoContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_font_sizes/font_size = 6
text = "游戏规则说明"
horizontal_alignment = 1
vertical_alignment = 1
autowrap_mode = TextServer.AUTOWRAP_WORD_SMART

[node name="GameStatusLabel" type="Label" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_font_sizes/font_size = 8
text = "游戏状态"
horizontal_alignment = 1
vertical_alignment = 1

[node name="ActionContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 3

[node name="NewGameButton" type="Button" parent="MainContainer/ActionContainer"]
custom_minimum_size = Vector2(60, 20)
layout_mode = 2
theme_override_font_sizes/font_size = 8
text = "新游戏"

[node name="ActionButton" type="Button" parent="MainContainer/ActionContainer"]
custom_minimum_size = Vector2(60, 20)
layout_mode = 2
theme_override_font_sizes/font_size = 8
text = "动作"

class_name BaseGame
extends RefCounted

# 基础游戏信号
signal game_started()
signal game_ended(result: Dictionary)
signal card_dealt(card: PokerCard, target: String)
signal turn_changed(current_player: String)
signal game_state_changed(new_state: String)

# 游戏状态枚举
enum GameState {
	WAITING,    # 等待开始
	DEALING,    # 发牌中
	PLAYING,    # 游戏进行中
	GAME_OVER   # 游戏结束
}

# 基础属性
var deck: Array[PokerCard] = []
var current_state: GameState = GameState.WAITING
var players: Array[Dictionary] = []
var current_player_index: int = 0

# 初始化
func _init():
	initialize_deck()

# 初始化牌组（标准52张牌）
func initialize_deck():
	deck.clear()
	
	# 创建标准52张牌
	for suit in PokerCard.Suit.values():
		if suit == PokerCard.Suit.CLUBS or suit == PokerCard.Suit.DIAMONDS or suit == PokerCard.Suit.HEARTS or suit == PokerCard.Suit.SPADES:
			for rank in range(PokerCard.Rank.ACE, PokerCard.Rank.KING + 1):
				var card = PokerCard.new(suit, rank)
				deck.append(card)
	
	# 洗牌
	shuffle_deck()

# 洗牌
func shuffle_deck():
	for i in range(deck.size()):
		var j = randi() % deck.size()
		var temp = deck[i]
		deck[i] = deck[j]
		deck[j] = temp

# 发牌
func deal_card() -> PokerCard:
	if deck.is_empty():
		return null
	return deck.pop_back()

# 开始新游戏（虚拟方法，子类需要重写）
func start_new_game():
	current_state = GameState.DEALING
	game_started.emit()

# 结束游戏（虚拟方法，子类需要重写）
func end_game(result: Dictionary = {}):
	current_state = GameState.GAME_OVER
	game_ended.emit(result)

# 获取当前状态
func get_current_state() -> GameState:
	return current_state

# 设置状态
func set_state(new_state: GameState):
	if current_state != new_state:
		current_state = new_state
		game_state_changed.emit(get_state_name(new_state))

# 获取状态名称
func get_state_name(state: GameState) -> String:
	match state:
		GameState.WAITING:
			return "等待开始"
		GameState.DEALING:
			return "发牌中"
		GameState.PLAYING:
			return "游戏中"
		GameState.GAME_OVER:
			return "游戏结束"
		_:
			return "未知状态"

# 添加玩家
func add_player(player_name: String, player_data: Dictionary = {}):
	var player = {
		"name": player_name,
		"hand": [],
		"data": player_data
	}
	players.append(player)

# 获取玩家
func get_player(index: int) -> Dictionary:
	if index >= 0 and index < players.size():
		return players[index]
	return {}

# 获取玩家数量
func get_player_count() -> int:
	return players.size()

# 清空所有玩家手牌
func clear_all_hands():
	for player in players:
		player.hand.clear()

# 检查牌组是否需要重新洗牌
func check_deck_size(min_cards: int = 10):
	if deck.size() < min_cards:
		initialize_deck()

# 获取游戏名称（虚拟方法，子类需要重写）
func get_game_name() -> String:
	return "基础游戏"

# 获取游戏规则说明（虚拟方法，子类需要重写）
func get_game_rules() -> String:
	return "这是一个基础游戏类"

# 检查游戏是否可以开始
func can_start_game() -> bool:
	return current_state == GameState.WAITING and players.size() > 0

# 检查游戏是否结束
func is_game_over() -> bool:
	return current_state == GameState.GAME_OVER

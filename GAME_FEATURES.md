# 扑克游戏集合 - 功能特性

## 🎮 游戏总览

### 21种游戏分类

#### 🃏 基础扑克游戏 (4种)
1. **21点** - 经典庄家对战，目标21点
2. **德州扑克** - 2张底牌+5张公共牌，支持下注
3. **争上游** - 3人快速出牌竞赛
4. **斗地主** - 地主vs农民，经典中国扑克

#### 🎯 经典扑克变种 (6种)
5. **锄大地** - 4人游戏，2最大，花色分等级
6. **奥马哈** - 4张底牌必须用2张组合
7. **七张梭哈** - 明暗牌结合的传统玩法
8. **五张抽** - 可换牌改善手牌的经典扑克
9. **Razz低牌** - 追求最低牌型的反向扑克
10. **2-7三次换** - 三轮换牌的策略游戏

#### 🌈 花色游戏 (2种)
11. **拱猪** - 避免分数牌的团队合作游戏
12. **红心大战** - 避免红心和黑桃Q的经典游戏

#### 🎲 休闲游戏 (5种)
13. **疯狂八** - 8可改变花色的快节奏游戏
14. **钓鱼** - 收集四张相同牌面的策略游戏
15. **记忆翻牌** - 考验记忆力的配对游戏
16. **比大小** - 简单直接的牌面比较战争
17. **抽乌龟** - 避免老K的运气与策略游戏

#### 🚧 待实现游戏 (4种)
18. **七张梭哈** - 需要专用UI实现
19. **五张抽** - 需要专用UI实现
20. **Razz低牌** - 需要专用UI实现
21. **2-7三次换** - 需要专用UI实现

## 🛠️ 技术特性

### 核心架构
- **模块化设计** - 每种游戏独立实现
- **统一接口** - 所有游戏继承BaseGame
- **动态切换** - 运行时无缝切换游戏类型
- **扩展性强** - 易于添加新游戏类型

### UI系统
- **专用UI** - 前4种游戏有专门设计的界面
- **通用UI** - 新游戏使用通用界面快速支持
- **响应式设计** - 适配50像素高度底栏
- **动画效果** - 发牌、翻牌等动画

### 游戏管理
- **游戏选择器** - 可视化游戏选择界面
- **快速切换** - 左上角按钮循环切换
- **键盘快捷键** - 数字键1-4快速切换
- **状态管理** - 完整的游戏状态跟踪

## 🎯 游戏特色功能

### 21点
- 庄家AI智能决策
- 动态分数计算
- 爆牌检测

### 德州扑克
- 完整下注轮次
- 看牌/跟注动态按钮
- 公共牌发牌动画

### 争上游
- 多种牌型支持
- 智能出牌验证
- 排名系统

### 斗地主
- 叫地主机制
- 地主牌分配
- 经典牌型识别

### 锄大地
- 花色等级系统
- 方块3开局规则
- 多人排名

### 奥马哈
- 强制2+3组合规则
- 完整下注系统
- 牌型评估

### 红心大战
- 传牌机制
- 分数计算
- 破红心规则

### 比大小
- 战争机制
- 牌堆管理
- 简单直观

### 记忆翻牌
- 4x4游戏板
- 配对检测
- 时间和步数统计

### 钓鱼
- 书的收集机制
- 智能AI询问
- 动态手牌管理

### 疯狂八
- 花色变换
- UNO机制
- 抽牌逻辑

### 抽乌龟
- 对子移除
- 随机抽牌
- 乌龟检测

## 🎨 视觉特性

### 卡牌系统
- 使用Assets/poker/light主题素材
- 支持正反面显示
- 选择状态高亮
- 发牌动画效果

### 界面设计
- 紧凑的底栏布局
- 清晰的状态显示
- 直观的操作按钮
- 实时信息更新

## 🔧 开发特性

### 代码质量
- 类型安全的GDScript
- 完整的错误处理
- 详细的调试信息
- 模块化架构

### 扩展性
- 易于添加新游戏
- 统一的信号系统
- 可复用的UI组件
- 灵活的配置系统

### 维护性
- 清晰的代码结构
- 完整的文档说明
- 一致的命名规范
- 模块间低耦合

## 🚀 未来规划

### 短期目标
- 完善剩余4种游戏的专用UI
- 优化AI智能程度
- 添加更多动画效果
- 改进用户体验

### 长期目标
- 支持网络多人游戏
- 添加更多游戏变种
- 实现游戏统计功能
- 支持自定义规则

## 📊 技术统计

- **总代码文件**: 30+
- **游戏类**: 17个已实现
- **UI类**: 8个（4个专用 + 1个通用 + 3个基础）
- **支持的牌型**: 50+ 种不同组合
- **AI难度**: 3个等级（简单、中等、困难）
- **动画效果**: 10+ 种不同动画
- **适配分辨率**: 支持多种屏幕尺寸

# 多种扑克游戏实现说明

## 概述
实现了包含21种不同类型的扑克和纸牌游戏，涵盖经典扑克、花色游戏、休闲游戏等多个类别。

## 新增功能

### 1. 游戏管理系统
- **GameManager.gd**: 游戏管理器，负责管理不同游戏类型的切换
- **GameSelector.gd**: 游戏选择器，提供游戏选择界面
- **BaseGame.gd**: 基础游戏类，所有游戏的父类

### 2. 21种扑克和纸牌游戏

#### 基础扑克游戏
- **21点** (BlackjackGame.gd) - 经典21点，玩家vs庄家
- **德州扑克** (TexasHoldemGame.gd) - 2张底牌+5张公共牌
- **争上游** (ShengJiGame.gd) - 3人游戏，先出完牌获胜
- **斗地主** (DouDiZhuGame.gd) - 地主vs农民经典玩法

#### 经典扑克变种
- **锄大地** (BigTwoGame.gd) - 4人游戏，2最大，花色有等级
- **奥马哈** (OmahaGame.gd) - 4张底牌必须用2张
- **七张梭哈** (SevenStudGame.gd) - 明暗牌结合的经典玩法
- **五张抽** (FiveDrawGame.gd) - 可换牌改善手牌
- **Razz低牌** (RazzGame.gd) - 目标是最低牌型
- **2-7三次换** (DeuceSevenGame.gd) - 三轮换牌机会

#### 花色游戏
- **拱猪** (SpadesGame.gd) - 避免得到分数牌
- **红心大战** (HeartsGame.gd) - 避免红心和黑桃Q

#### 休闲游戏
- **疯狂八** (CrazyEightsGame.gd) - 8可以改变花色
- **钓鱼** (GoFishGame.gd) - 收集四张相同点数的牌
- **记忆翻牌** (MemoryGame.gd) - 翻开相同牌配对
- **比大小** (WarGame.gd) - 简单的牌面比较
- **抽乌龟** (OldMaidGame.gd) - 避免最后剩下老K

### 3. UI系统

#### 基础UI (BaseGameUI.gd)
- 所有游戏UI的基类
- 提供通用的卡牌管理和动画功能

#### 专用UI
- **BlackjackUI.gd**: 21点游戏界面
- **TexasHoldemUI.gd**: 德州扑克游戏界面
- **ShengJiUI.gd**: 争上游游戏界面
- **DouDiZhuUI.gd**: 斗地主游戏界面
- **MemoryGameUI.gd**: 记忆翻牌游戏界面（4x4网格）

### 4. 游戏选择功能
- 右上角"选择游戏"按钮
- 弹出式游戏选择界面
- 支持游戏间无缝切换

## 文件结构

```
Scene/
├── GameManager.gd              # 游戏管理器
├── GameSelector.gd             # 游戏选择器
├── GameSelector.tscn           # 游戏选择器场景
├── Poker/
│   ├── BaseGame.gd            # 基础游戏类
│   ├── BlackjackGame.gd       # 21点游戏
│   ├── TexasHoldemGame.gd     # 德州扑克游戏
│   ├── ShengJiGame.gd         # 争上游游戏
│   ├── DouDiZhuGame.gd        # 斗地主游戏
│   ├── PokerCard.gd           # 扑克牌类（已存在）
│   └── PokerCardUI.gd         # 扑克牌UI（已存在，新增选择功能）
└── UI/
    ├── BaseGameUI.gd          # 基础游戏UI
    ├── BlackjackUI.gd         # 21点UI
    ├── BlackjackUI.tscn       # 21点UI场景
    ├── TexasHoldemUI.gd       # 德州扑克UI
    ├── TexasHoldemUI.tscn     # 德州扑克UI场景
    ├── ShengJiUI.gd           # 争上游UI
    ├── ShengJiUI.tscn         # 争上游UI场景
    ├── DouDiZhuUI.gd          # 斗地主UI
    ├── DouDiZhuUI.tscn        # 斗地主UI场景
    ├── main_page.gd           # 主页面（已修改）
    └── main_page.tscn         # 主页面场景（已修改）
```

## 使用方法

1. **启动游戏**: 默认加载21点游戏
2. **切换游戏**: 点击左上角的游戏名称按钮循环切换游戏：
   - 21点 → 德州扑克 → 争上游 → 斗地主 → 21点...
3. **开始游戏**: 点击"新游戏"按钮开始当前游戏
4. **游戏操作**: 根据不同游戏类型使用相应的操作按钮
5. **键盘快捷键**: 也可以使用数字键1-4快速切换游戏

## 游戏操作说明

### 21点
- **要牌**: 获得一张新牌
- **停牌**: 结束回合，庄家开始行动

### 德州扑克
- **看牌**: 当前下注为0时，可以免费看牌
- **跟注**: 跟上当前下注额（按钮会显示具体金额）
- **加注**: 增加下注额（固定加注50）
- **弃牌**: 放弃当前手牌

### 争上游
- **选择牌**: 点击手牌选择要出的牌
- **出牌**: 出选中的牌
- **过牌**: 跳过当前回合

### 斗地主
- **叫地主**: 在叫地主阶段选择成为地主
- **不叫**: 在叫地主阶段选择不当地主
- **选择牌**: 点击手牌选择要出的牌
- **出牌**: 出选中的牌
- **过牌**: 跳过当前回合

## 技术特点

1. **模块化设计**: 每种游戏都是独立的类，便于维护和扩展
2. **统一接口**: 所有游戏都继承自BaseGame，提供一致的接口
3. **动态切换**: 支持运行时切换不同游戏类型
4. **扩展性**: 易于添加新的游戏类型
5. **UI适配**: 所有游戏UI都适配50像素高度的底栏

## 注意事项

1. 所有游戏都使用相同的扑克牌素材（Assets/poker/light主题）
2. AI逻辑目前是简化实现，可以根据需要进一步优化
3. 游戏规则实现了基本功能，可以根据需要添加更多细节
4. 卡牌动画和视觉效果已经集成到基础UI系统中

## 已修复的问题

### v1.1 更新
- ✅ 修复了德州扑克切换后无法再切换的问题
- ✅ 修复了数组类型不匹配的编译错误
- ✅ 优化了游戏切换按钮的层级管理
- ✅ 增强了按钮存在性检查和自动重建机制
- ✅ 添加了详细的调试信息输出

### v1.2 更新
- ✅ 修复了德州扑克0注（看牌）功能
- ✅ 动态更新按钮文本显示正确操作和金额
- ✅ 改进了AI行为，降低弃牌概率
- ✅ 增强了游戏状态显示和用户反馈
- ✅ 优化了下注轮次的UI更新时机

### v2.0 重大更新 - 21种游戏大集合
- ✅ 新增17种扑克和纸牌游戏
- ✅ 实现了经典扑克变种（锄大地、奥马哈、七张梭哈等）
- ✅ 添加了花色游戏（拱猪、红心大战）
- ✅ 包含了休闲游戏（疯狂八、钓鱼、记忆翻牌、比大小、抽乌龟）
- ✅ 创建了通用游戏UI系统，支持快速添加新游戏
- ✅ 扩展了游戏管理器，支持21种不同游戏类型
- ✅ 保持了统一的游戏切换体验

### v2.1 更新 - 记忆翻牌专用UI
- ✅ 修复了MemoryGame中get_tree()调用错误
- ✅ 创建了记忆翻牌游戏专用UI（4x4网格布局）
- ✅ 实现了卡牌翻牌动画和配对检测
- ✅ 添加了提示功能和重新开始功能
- ✅ 优化了游戏逻辑和UI的分离

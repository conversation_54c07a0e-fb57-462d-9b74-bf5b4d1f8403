# 多种扑克游戏实现说明

## 概述
在原有21点游戏的基础上，新增了德州扑克、争上游和斗地主三种扑克游戏，并实现了游戏选择功能。

## 新增功能

### 1. 游戏管理系统
- **GameManager.gd**: 游戏管理器，负责管理不同游戏类型的切换
- **GameSelector.gd**: 游戏选择器，提供游戏选择界面
- **BaseGame.gd**: 基础游戏类，所有游戏的父类

### 2. 四种扑克游戏

#### 21点 (BlackjackGame.gd)
- 经典21点游戏规则
- 玩家vs庄家
- 目标：手牌总和接近21但不超过

#### 德州扑克 (TexasHoldemGame.gd)
- 每人2张底牌 + 5张公共牌
- 包含翻牌前、翻牌、转牌、河牌、摊牌阶段
- 支持跟注、加注、弃牌操作

#### 争上游 (ShengJiGame.gd)
- 3人游戏，每人17张牌
- 目标：最先出完手牌
- 支持单张、对子、三张、顺子、炸弹等牌型

#### 斗地主 (DouDiZhuGame.gd)
- 3人游戏，包含叫地主和出牌阶段
- 地主vs农民
- 支持各种经典斗地主牌型

### 3. UI系统

#### 基础UI (BaseGameUI.gd)
- 所有游戏UI的基类
- 提供通用的卡牌管理和动画功能

#### 专用UI
- **BlackjackUI.gd**: 21点游戏界面
- **TexasHoldemUI.gd**: 德州扑克游戏界面
- **ShengJiUI.gd**: 争上游游戏界面
- **DouDiZhuUI.gd**: 斗地主游戏界面

### 4. 游戏选择功能
- 右上角"选择游戏"按钮
- 弹出式游戏选择界面
- 支持游戏间无缝切换

## 文件结构

```
Scene/
├── GameManager.gd              # 游戏管理器
├── GameSelector.gd             # 游戏选择器
├── GameSelector.tscn           # 游戏选择器场景
├── Poker/
│   ├── BaseGame.gd            # 基础游戏类
│   ├── BlackjackGame.gd       # 21点游戏
│   ├── TexasHoldemGame.gd     # 德州扑克游戏
│   ├── ShengJiGame.gd         # 争上游游戏
│   ├── DouDiZhuGame.gd        # 斗地主游戏
│   ├── PokerCard.gd           # 扑克牌类（已存在）
│   └── PokerCardUI.gd         # 扑克牌UI（已存在，新增选择功能）
└── UI/
    ├── BaseGameUI.gd          # 基础游戏UI
    ├── BlackjackUI.gd         # 21点UI
    ├── BlackjackUI.tscn       # 21点UI场景
    ├── TexasHoldemUI.gd       # 德州扑克UI
    ├── TexasHoldemUI.tscn     # 德州扑克UI场景
    ├── ShengJiUI.gd           # 争上游UI
    ├── ShengJiUI.tscn         # 争上游UI场景
    ├── DouDiZhuUI.gd          # 斗地主UI
    ├── DouDiZhuUI.tscn        # 斗地主UI场景
    ├── main_page.gd           # 主页面（已修改）
    └── main_page.tscn         # 主页面场景（已修改）
```

## 使用方法

1. **启动游戏**: 默认加载21点游戏
2. **切换游戏**: 点击左上角的游戏名称按钮循环切换游戏：
   - 21点 → 德州扑克 → 争上游 → 斗地主 → 21点...
3. **开始游戏**: 点击"新游戏"按钮开始当前游戏
4. **游戏操作**: 根据不同游戏类型使用相应的操作按钮
5. **键盘快捷键**: 也可以使用数字键1-4快速切换游戏

## 游戏操作说明

### 21点
- **要牌**: 获得一张新牌
- **停牌**: 结束回合，庄家开始行动

### 德州扑克
- **看牌**: 当前下注为0时，可以免费看牌
- **跟注**: 跟上当前下注额（按钮会显示具体金额）
- **加注**: 增加下注额（固定加注50）
- **弃牌**: 放弃当前手牌

### 争上游
- **选择牌**: 点击手牌选择要出的牌
- **出牌**: 出选中的牌
- **过牌**: 跳过当前回合

### 斗地主
- **叫地主**: 在叫地主阶段选择成为地主
- **不叫**: 在叫地主阶段选择不当地主
- **选择牌**: 点击手牌选择要出的牌
- **出牌**: 出选中的牌
- **过牌**: 跳过当前回合

## 技术特点

1. **模块化设计**: 每种游戏都是独立的类，便于维护和扩展
2. **统一接口**: 所有游戏都继承自BaseGame，提供一致的接口
3. **动态切换**: 支持运行时切换不同游戏类型
4. **扩展性**: 易于添加新的游戏类型
5. **UI适配**: 所有游戏UI都适配50像素高度的底栏

## 注意事项

1. 所有游戏都使用相同的扑克牌素材（Assets/poker/light主题）
2. AI逻辑目前是简化实现，可以根据需要进一步优化
3. 游戏规则实现了基本功能，可以根据需要添加更多细节
4. 卡牌动画和视觉效果已经集成到基础UI系统中

## 已修复的问题

### v1.1 更新
- ✅ 修复了德州扑克切换后无法再切换的问题
- ✅ 修复了数组类型不匹配的编译错误
- ✅ 优化了游戏切换按钮的层级管理
- ✅ 增强了按钮存在性检查和自动重建机制
- ✅ 添加了详细的调试信息输出

### v1.2 更新
- ✅ 修复了德州扑克0注（看牌）功能
- ✅ 动态更新按钮文本显示正确操作和金额
- ✅ 改进了AI行为，降低弃牌概率
- ✅ 增强了游戏状态显示和用户反馈
- ✅ 优化了下注轮次的UI更新时机

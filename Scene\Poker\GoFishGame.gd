class_name GoFishGame
extends BaseGame

# 钓鱼游戏特有信号
signal cards_requested(requester: String, target: String, rank: PokerCard.Rank)
signal cards_given(giver: String, receiver: String, cards: Array)
signal go_fish_called(player: String)
signal book_completed(player: String, rank: PokerCard.Rank)

# 游戏状态
var player_hands: Array[Array] = [[], []]  # 2个玩家的手牌
var player_books: Array[Array] = [[], []]  # 玩家的书（4张相同牌面）
var deck_remaining: Array = []             # 剩余牌堆
var current_turn: int = 0                  # 当前回合玩家

func _init():
	super._init()
	# 添加玩家和AI
	add_player("玩家")
	add_player("AI")

# 获取游戏名称
func get_game_name() -> String:
	return "钓鱼"

# 获取游戏规则
func get_game_rules() -> String:
	return "收集4张相同点数的牌组成'书'。向对手要牌，没有则'钓鱼'从牌堆抽牌。"

# 开始新游戏
func start_new_game():
	super.start_new_game()
	
	# 重置游戏状态
	reset_game_state()
	
	# 发牌
	deal_initial_cards()
	
	# 检查初始书
	check_initial_books()
	
	# 开始游戏
	current_turn = 0
	set_state(GameState.PLAYING)
	turn_changed.emit("玩家")

# 重置游戏状态
func reset_game_state():
	for i in range(2):
		player_hands[i].clear()
		player_books[i].clear()
		players[i].hand.clear()
	
	deck_remaining.clear()
	current_turn = 0

# 发初始牌（每人7张）
func deal_initial_cards():
	check_deck_size(52)
	
	# 每人发7张牌
	for round in range(7):
		for player_index in range(2):
			var card = deal_card()
			card.is_face_up = (player_index == 0)  # 只有玩家的牌正面朝上
			player_hands[player_index].append(card)
			players[player_index].hand.append(card)
			card_dealt.emit(card, players[player_index].name)
	
	# 剩余牌放入牌堆
	deck_remaining = deck.duplicate()
	
	# 对玩家手牌排序
	sort_hand(player_hands[0])

# 对手牌排序
func sort_hand(hand: Array):
	hand.sort_custom(func(a, b): return a.rank < b.rank)

# 检查初始书
func check_initial_books():
	for player_index in range(2):
		check_and_remove_books(player_index)

# 检查并移除书
func check_and_remove_books(player_index: int):
	var hand = player_hands[player_index]
	var rank_counts = {}
	
	# 统计每种牌面的数量
	for card in hand:
		var rank = card.rank
		if rank in rank_counts:
			rank_counts[rank] += 1
		else:
			rank_counts[rank] = 1
	
	# 移除4张相同的牌面
	for rank in rank_counts:
		if rank_counts[rank] >= 4:
			var book_cards = []
			var cards_to_remove = []
			
			for card in hand:
				if card.rank == rank and book_cards.size() < 4:
					book_cards.append(card)
					cards_to_remove.append(card)
			
			# 从手牌中移除
			for card in cards_to_remove:
				hand.erase(card)
				players[player_index].hand.erase(card)
			
			# 添加到书中
			player_books[player_index].append(book_cards)
			book_completed.emit(players[player_index].name, rank)

# 玩家要牌
func player_ask_for_cards(rank: PokerCard.Rank) -> bool:
	if current_turn != 0 or current_state != GameState.PLAYING:
		return false
	
	# 检查玩家手中是否有这种牌
	if not has_rank_in_hand(0, rank):
		return false
	
	cards_requested.emit("玩家", "AI", rank)
	
	# 检查AI是否有这种牌
	var ai_cards = get_cards_of_rank(1, rank)
	
	if not ai_cards.is_empty():
		# AI有牌，给玩家
		give_cards(1, 0, ai_cards)
		
		# 检查玩家是否组成新书
		check_and_remove_books(0)
		
		# 玩家继续回合
		turn_changed.emit("玩家")
	else:
		# AI没有牌，钓鱼
		go_fish_called.emit("AI")
		player_go_fish()
	
	return true

# 玩家钓鱼
func player_go_fish():
	if deck_remaining.is_empty():
		# 牌堆空了，结束游戏
		end_game_by_empty_deck()
		return
	
	# 从牌堆抽一张牌
	var card = deck_remaining.pop_back()
	card.is_face_up = true
	player_hands[0].append(card)
	players[0].hand.append(card)
	
	card_dealt.emit(card, "玩家")
	
	# 重新排序
	sort_hand(player_hands[0])
	
	# 检查是否组成新书
	check_and_remove_books(0)
	
	# 轮到AI
	current_turn = 1
	ai_turn()

# AI回合
func ai_turn():
	if current_state != GameState.PLAYING:
		return
	
	var ai_hand = player_hands[1]
	if ai_hand.is_empty():
		check_game_end()
		return
	
	# AI随机选择一种牌面要牌
	var rank = ai_hand[randi() % ai_hand.size()].rank
	
	cards_requested.emit("AI", "玩家", rank)
	
	# 检查玩家是否有这种牌
	var player_cards = get_cards_of_rank(0, rank)
	
	if not player_cards.is_empty():
		# 玩家有牌，给AI
		give_cards(0, 1, player_cards)
		
		# 检查AI是否组成新书
		check_and_remove_books(1)
		
		# AI继续回合
		ai_turn()
	else:
		# 玩家没有牌，AI钓鱼
		go_fish_called.emit("玩家")
		ai_go_fish()

# AI钓鱼
func ai_go_fish():
	if deck_remaining.is_empty():
		# 牌堆空了，结束游戏
		end_game_by_empty_deck()
		return
	
	# 从牌堆抽一张牌
	var card = deck_remaining.pop_back()
	card.is_face_up = false  # AI的牌背面朝上
	player_hands[1].append(card)
	players[1].hand.append(card)
	
	card_dealt.emit(card, "AI")
	
	# 检查是否组成新书
	check_and_remove_books(1)
	
	# 轮到玩家
	current_turn = 0
	turn_changed.emit("玩家")

# 检查手中是否有指定牌面
func has_rank_in_hand(player_index: int, rank: PokerCard.Rank) -> bool:
	for card in player_hands[player_index]:
		if card.rank == rank:
			return true
	return false

# 获取指定牌面的所有牌
func get_cards_of_rank(player_index: int, rank: PokerCard.Rank) -> Array:
	var cards: Array = []
	for card in player_hands[player_index]:
		if card.rank == rank:
			cards.append(card)
	return cards

# 给牌
func give_cards(giver_index: int, receiver_index: int, cards: Array):
	for card in cards:
		# 从给牌者手中移除
		player_hands[giver_index].erase(card)
		players[giver_index].hand.erase(card)
		
		# 添加到接收者手中
		card.is_face_up = (receiver_index == 0)  # 玩家的牌正面朝上
		player_hands[receiver_index].append(card)
		players[receiver_index].hand.append(card)
	
	cards_given.emit(players[giver_index].name, players[receiver_index].name, cards)
	
	# 重新排序接收者手牌
	if receiver_index == 0:
		sort_hand(player_hands[0])

# 检查游戏是否结束
func check_game_end():
	# 检查是否有玩家手牌为空
	for i in range(2):
		if player_hands[i].is_empty():
			end_game_by_empty_hand()
			return
	
	# 检查牌堆是否为空
	if deck_remaining.is_empty():
		end_game_by_empty_deck()

# 因手牌为空结束游戏
func end_game_by_empty_hand():
	var player_book_count = player_books[0].size()
	var ai_book_count = player_books[1].size()
	
	var result = {}
	if player_book_count > ai_book_count:
		result = {"winner": "玩家", "reason": "书的数量更多"}
	elif ai_book_count > player_book_count:
		result = {"winner": "AI", "reason": "书的数量更多"}
	else:
		result = {"winner": "平局", "reason": "书的数量相同"}
	
	result["player_books"] = player_book_count
	result["ai_books"] = ai_book_count
	
	end_game(result)

# 因牌堆为空结束游戏
func end_game_by_empty_deck():
	var player_book_count = player_books[0].size()
	var ai_book_count = player_books[1].size()
	
	var result = {}
	if player_book_count > ai_book_count:
		result = {"winner": "玩家", "reason": "书的数量更多"}
	elif ai_book_count > player_book_count:
		result = {"winner": "AI", "reason": "书的数量更多"}
	else:
		result = {"winner": "平局", "reason": "书的数量相同"}
	
	result["player_books"] = player_book_count
	result["ai_books"] = ai_book_count
	
	end_game(result)

# 获取玩家手牌
func get_player_hand() -> Array:
	return player_hands[0]

# 获取AI手牌数量
func get_ai_hand_count() -> int:
	return player_hands[1].size()

# 获取玩家书的数量
func get_player_book_count() -> int:
	return player_books[0].size()

# 获取AI书的数量
func get_ai_book_count() -> int:
	return player_books[1].size()

# 获取剩余牌堆数量
func get_deck_remaining_count() -> int:
	return deck_remaining.size()

# 获取当前回合玩家
func get_current_turn() -> int:
	return current_turn

# 获取可要的牌面（玩家手中有的牌面）
func get_available_ranks() -> Array:
	var ranks: Array = []
	for card in player_hands[0]:
		if not card.rank in ranks:
			ranks.append(card.rank)
	return ranks

extends SceneTree

func _init():
	print("开始测试游戏系统...")
	
	# 测试游戏管理器
	test_game_manager()
	
	# 测试各种游戏
	test_blackjack_game()
	test_texas_holdem_game()
	test_shengji_game()
	test_doudizhu_game()
	
	print("所有测试完成！")
	quit()

func test_game_manager():
	print("\n=== 测试游戏管理器 ===")
	var manager = GameManager.new()
	
	# 测试游戏类型名称
	for game_type in manager.get_all_game_types():
		var name = manager.get_game_type_name(game_type)
		var desc = manager.get_game_type_description(game_type)
		print("游戏类型: %s - %s" % [name, desc])
	
	print("游戏管理器测试完成")

func test_blackjack_game():
	print("\n=== 测试21点游戏 ===")
	var game = BlackjackGame.new()
	
	print("游戏名称: ", game.get_game_name())
	print("游戏规则: ", game.get_game_rules())
	print("初始状态: ", game.get_state_name(game.get_current_state()))
	
	# 测试开始游戏
	game.start_new_game()
	print("开始游戏后状态: ", game.get_state_name(game.get_current_state()))
	print("玩家手牌数量: ", game.get_player_hand().size())
	print("庄家手牌数量: ", game.get_dealer_hand().size())
	
	print("21点游戏测试完成")

func test_texas_holdem_game():
	print("\n=== 测试德州扑克游戏 ===")
	var game = TexasHoldemGame.new()
	
	print("游戏名称: ", game.get_game_name())
	print("游戏规则: ", game.get_game_rules())
	print("初始状态: ", game.get_state_name(game.get_current_state()))
	
	# 测试开始游戏
	game.start_new_game()
	print("开始游戏后状态: ", game.get_state_name(game.get_current_state()))
	print("当前阶段: ", game.get_phase_name())
	
	print("德州扑克游戏测试完成")

func test_shengji_game():
	print("\n=== 测试争上游游戏 ===")
	var game = ShengJiGame.new()
	
	print("游戏名称: ", game.get_game_name())
	print("游戏规则: ", game.get_game_rules())
	print("初始状态: ", game.get_state_name(game.get_current_state()))
	
	# 测试开始游戏
	game.start_new_game()
	print("开始游戏后状态: ", game.get_state_name(game.get_current_state()))
	print("玩家手牌数量: ", game.get_player_hand().size())
	
	print("争上游游戏测试完成")

func test_doudizhu_game():
	print("\n=== 测试斗地主游戏 ===")
	var game = DouDiZhuGame.new()
	
	print("游戏名称: ", game.get_game_name())
	print("游戏规则: ", game.get_game_rules())
	print("初始状态: ", game.get_state_name(game.get_current_state()))
	
	# 测试开始游戏
	game.start_new_game()
	print("开始游戏后状态: ", game.get_state_name(game.get_current_state()))
	print("当前阶段: ", game.get_current_phase())
	print("玩家手牌数量: ", game.get_player_hand().size())
	
	print("斗地主游戏测试完成")

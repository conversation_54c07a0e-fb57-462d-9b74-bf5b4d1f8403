class_name WarGame
extends BaseGame

# 比大小特有信号
signal cards_revealed(player_card: PokerCard, ai_card: PokerCard)
signal war_started()
signal round_won(winner: String, cards_won: Array)

# 游戏状态
var player_deck: Array = []    # 玩家牌堆
var ai_deck: Array = []        # AI牌堆
var player_card: PokerCard = null  # 玩家当前牌
var ai_card: PokerCard = null      # AI当前牌
var war_pile: Array = []       # 战争牌堆
var is_war: bool = false       # 是否在战争状态

func _init():
	super._init()
	# 添加玩家和AI
	add_player("玩家")
	add_player("AI")

# 获取游戏名称
func get_game_name() -> String:
	return "比大小"

# 获取游戏规则
func get_game_rules() -> String:
	return "每人一半牌，同时翻牌比大小。A最大，2最小。平局则战争（各出3张牌再比）。"

# 开始新游戏
func start_new_game():
	super.start_new_game()
	
	# 重置游戏状态
	reset_game_state()
	
	# 分发牌组
	deal_cards()
	
	# 开始游戏
	set_state(GameState.PLAYING)
	turn_changed.emit("点击翻牌开始")

# 重置游戏状态
func reset_game_state():
	player_deck.clear()
	ai_deck.clear()
	war_pile.clear()
	player_card = null
	ai_card = null
	is_war = false
	
	for player in players:
		player.hand.clear()

# 分发牌组（每人26张）
func deal_cards():
	check_deck_size(52)
	
	# 洗牌后分成两堆
	for i in range(52):
		var card = deal_card()
		card.is_face_up = false  # 都是背面朝下
		
		if i % 2 == 0:
			player_deck.append(card)
			players[0].hand.append(card)
		else:
			ai_deck.append(card)
			players[1].hand.append(card)
	
	print("玩家牌数：", player_deck.size(), " AI牌数：", ai_deck.size())

# 玩家翻牌
func player_flip_card() -> bool:
	if current_state != GameState.PLAYING:
		return false
	
	if player_deck.is_empty() or ai_deck.is_empty():
		return false
	
	# 翻牌
	player_card = player_deck.pop_front()
	ai_card = ai_deck.pop_front()
	
	player_card.is_face_up = true
	ai_card.is_face_up = true
	
	# 添加到战争牌堆
	war_pile.append(player_card)
	war_pile.append(ai_card)
	
	cards_revealed.emit(player_card, ai_card)
	
	# 比较牌面大小
	compare_cards()
	
	return true

# 比较牌面大小
func compare_cards():
	var player_value = get_card_war_value(player_card)
	var ai_value = get_card_war_value(ai_card)
	
	if player_value > ai_value:
		# 玩家获胜
		player_wins_round()
	elif ai_value > player_value:
		# AI获胜
		ai_wins_round()
	else:
		# 平局，开始战争
		start_war()

# 获取牌的战争价值（A最大，2最小）
func get_card_war_value(card: PokerCard) -> int:
	match card.rank:
		PokerCard.Rank.TWO:
			return 2
		PokerCard.Rank.THREE:
			return 3
		PokerCard.Rank.FOUR:
			return 4
		PokerCard.Rank.FIVE:
			return 5
		PokerCard.Rank.SIX:
			return 6
		PokerCard.Rank.SEVEN:
			return 7
		PokerCard.Rank.EIGHT:
			return 8
		PokerCard.Rank.NINE:
			return 9
		PokerCard.Rank.TEN:
			return 10
		PokerCard.Rank.JACK:
			return 11
		PokerCard.Rank.QUEEN:
			return 12
		PokerCard.Rank.KING:
			return 13
		PokerCard.Rank.ACE:
			return 14
		_:
			return 0

# 玩家获胜本轮
func player_wins_round():
	# 玩家获得所有战争牌堆的牌
	for card in war_pile:
		card.is_face_up = false
		player_deck.append(card)
		players[0].hand.append(card)
	
	round_won.emit("玩家", war_pile.duplicate())
	war_pile.clear()
	is_war = false
	
	# 检查游戏是否结束
	check_game_end()

# AI获胜本轮
func ai_wins_round():
	# AI获得所有战争牌堆的牌
	for card in war_pile:
		card.is_face_up = false
		ai_deck.append(card)
		players[1].hand.append(card)
	
	round_won.emit("AI", war_pile.duplicate())
	war_pile.clear()
	is_war = false
	
	# 检查游戏是否结束
	check_game_end()

# 开始战争
func start_war():
	is_war = true
	war_started.emit()
	
	# 检查是否有足够的牌进行战争
	if player_deck.size() < 3 or ai_deck.size() < 3:
		# 牌不够，直接判定胜负
		if player_deck.size() > ai_deck.size():
			player_wins_round()
		elif ai_deck.size() > player_deck.size():
			ai_wins_round()
		else:
			# 平局
			end_game({"winner": "平局", "reason": "牌数相等"})
		return
	
	# 各自出3张牌到战争牌堆（前2张背面，第3张正面比较）
	for i in range(3):
		if not player_deck.is_empty():
			var card = player_deck.pop_front()
			card.is_face_up = (i == 2)  # 第3张正面
			war_pile.append(card)
			if i == 2:
				player_card = card
		
		if not ai_deck.is_empty():
			var card = ai_deck.pop_front()
			card.is_face_up = (i == 2)  # 第3张正面
			war_pile.append(card)
			if i == 2:
				ai_card = card
	
	# 比较第3张牌
	cards_revealed.emit(player_card, ai_card)
	compare_cards()

# 检查游戏是否结束
func check_game_end():
	if player_deck.is_empty():
		# 玩家没牌了，AI获胜
		end_game({
			"winner": "AI", 
			"reason": "玩家没牌了",
			"player_cards": player_deck.size(),
			"ai_cards": ai_deck.size()
		})
	elif ai_deck.is_empty():
		# AI没牌了，玩家获胜
		end_game({
			"winner": "玩家", 
			"reason": "AI没牌了",
			"player_cards": player_deck.size(),
			"ai_cards": ai_deck.size()
		})
	else:
		# 游戏继续
		turn_changed.emit("点击翻牌继续")

# 获取玩家牌数
func get_player_card_count() -> int:
	return player_deck.size()

# 获取AI牌数
func get_ai_card_count() -> int:
	return ai_deck.size()

# 获取当前显示的牌
func get_current_player_card() -> PokerCard:
	return player_card

func get_current_ai_card() -> PokerCard:
	return ai_card

# 获取是否在战争状态
func is_in_war() -> bool:
	return is_war

# 获取战争牌堆大小
func get_war_pile_size() -> int:
	return war_pile.size()

# 自动翻牌（用于演示）
func auto_flip():
	if current_state == GameState.PLAYING:
		player_flip_card()

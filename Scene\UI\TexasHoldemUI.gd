class_name TexasHoldemUI
extends BaseGameUI

# 德州扑克UI组件
@onready var player_hole_container: HBoxContainer = $MainContainer/PlayerArea/HoleCardsContainer
@onready var ai_hole_container: HBoxContainer = $MainContainer/AIArea/HoleCardsContainer
@onready var community_container: HBoxContainer = $MainContainer/CenterArea/CommunityContainer
@onready var pot_label: Label = $MainContainer/CenterArea/PotLabel
@onready var phase_label: Label = $MainContainer/CenterArea/PhaseLabel
@onready var call_button: Button = $MainContainer/CenterArea/ButtonContainer/CallButton
@onready var raise_button: Button = $MainContainer/CenterArea/ButtonContainer/RaiseButton
@onready var fold_button: Button = $MainContainer/CenterArea/ButtonContainer/FoldButton
@onready var new_game_button: Button = $MainContainer/CenterArea/ButtonContainer/NewGameButton

# 卡牌UI数组
var player_hole_uis: Array[PokerCardUI] = []
var ai_hole_uis: Array[PokerCardUI] = []
var community_uis: Array[PokerCardUI] = []

func _ready():
	super._ready()
	
	# 设置组件引用
	game_status_label = $MainContainer/CenterArea/GameStatusLabel
	main_container = $MainContainer
	button_container = $MainContainer/CenterArea/ButtonContainer

func initialize_ui():
	# 连接按钮信号
	if call_button:
		call_button.pressed.connect(_on_call_button_pressed)
	if raise_button:
		raise_button.pressed.connect(_on_raise_button_pressed)
	if fold_button:
		fold_button.pressed.connect(_on_fold_button_pressed)
	if new_game_button:
		new_game_button.pressed.connect(_on_new_game_button_pressed)
	
	# 初始化状态
	update_status("点击新游戏开始德州扑克")
	update_ui_state()

func connect_game_signals():
	super.connect_game_signals()

	# 连接德州扑克特有信号
	if game_instance is TexasHoldemGame:
		var texas_game = game_instance as TexasHoldemGame
		texas_game.community_cards_dealt.connect(_on_community_cards_dealt)
		texas_game.betting_round_started.connect(_on_betting_round_started)
		texas_game.player_action.connect(_on_player_action)

# 重写游戏开始处理
func _on_game_started():
	super._on_game_started()
	# 延迟更新UI状态，确保游戏数据已初始化
	call_deferred("update_ui_state")

func _on_card_dealt(card: PokerCard, target: String):
	var card_ui = create_card_ui(card)
	
	match target:
		"玩家":
			add_card_ui_to_container(card_ui, player_hole_container)
			player_hole_uis.append(card_ui)
			play_deal_animation(card_ui, player_hole_uis.size() * 0.2)
		"AI":
			add_card_ui_to_container(card_ui, ai_hole_container)
			ai_hole_uis.append(card_ui)
			play_deal_animation(card_ui, ai_hole_uis.size() * 0.2)
		"公共牌":
			add_card_ui_to_container(card_ui, community_container)
			community_uis.append(card_ui)
			play_deal_animation(card_ui, community_uis.size() * 0.2)

func _on_community_cards_dealt(cards: Array[PokerCard]):
	update_ui_state()

func _on_betting_round_started(round_name: String):
	if phase_label:
		phase_label.text = "阶段: " + round_name
	update_status(round_name + "阶段开始")
	# 更新UI状态，确保按钮文本正确
	update_ui_state()

func _on_player_action(player_name: String, action: String, amount: int):
	var message = player_name + " " + action
	if amount > 0:
		message += " " + str(amount)
	show_message(message, 1.0)
	update_ui_state()

func _on_call_button_pressed():
	if game_instance is TexasHoldemGame:
		var texas_game = game_instance as TexasHoldemGame
		texas_game.player_call()

func _on_raise_button_pressed():
	if game_instance is TexasHoldemGame:
		var texas_game = game_instance as TexasHoldemGame
		texas_game.player_raise(50)  # 固定加注50

func _on_fold_button_pressed():
	if game_instance is TexasHoldemGame:
		var texas_game = game_instance as TexasHoldemGame
		texas_game.player_fold()

func _on_new_game_button_pressed():
	if game_instance:
		game_instance.start_new_game()

func update_ui_state():
	if not game_instance is TexasHoldemGame:
		return

	var texas_game = game_instance as TexasHoldemGame
	var state = texas_game.get_current_state()

	# 更新按钮状态
	var is_playing = (state == BaseGame.GameState.PLAYING)
	var current_bet = texas_game.current_bet
	var player_bet = texas_game.players[0].data.current_bet
	var call_amount = current_bet - player_bet

	if call_button:
		call_button.disabled = not is_playing
		# 动态更新按钮文本
		if call_amount == 0:
			call_button.text = "看牌"
		else:
			call_button.text = "跟注(" + str(call_amount) + ")"

	if raise_button:
		raise_button.disabled = not is_playing
	if fold_button:
		fold_button.disabled = not is_playing
	if new_game_button:
		new_game_button.disabled = (state == BaseGame.GameState.DEALING)

	# 更新底池显示
	if pot_label:
		pot_label.text = "底池: " + str(texas_game.pot)

	# 更新当前下注信息
	update_status("当前下注: " + str(current_bet) + " 你的下注: " + str(player_bet))

func clear_all_cards():
	super.clear_all_cards()
	
	# 清空各区域的牌
	for card_ui in player_hole_uis:
		if is_instance_valid(card_ui):
			card_ui.queue_free()
	player_hole_uis.clear()
	
	for card_ui in ai_hole_uis:
		if is_instance_valid(card_ui):
			card_ui.queue_free()
	ai_hole_uis.clear()
	
	for card_ui in community_uis:
		if is_instance_valid(card_ui):
			card_ui.queue_free()
	community_uis.clear()

func refresh_ui():
	update_ui_state()

[gd_scene load_steps=2 format=3 uid="uid://memory_game_ui"]

[ext_resource type="Script" path="res://Scene/UI/MemoryGameUI.gd" id="1_memory_ui"]

[node name="MemoryGameUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_memory_ui")

[node name="MainContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme_override_constants/separation = 5

[node name="GameBoardContainer" type="GridContainer" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
columns = 4

[node name="GameStatusLabel" type="Label" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 2
theme_override_font_sizes/font_size = 8
text = "游戏状态"
horizontal_alignment = 1
vertical_alignment = 1

[node name="StatsContainer" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 2
theme_override_constants/separation = 3

[node name="MovesLabel" type="Label" parent="MainContainer/StatsContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 8
text = "步数: 0"
horizontal_alignment = 1

[node name="TimeLabel" type="Label" parent="MainContainer/StatsContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 8
text = "时间: 0:00"
horizontal_alignment = 1

[node name="HintButton" type="Button" parent="MainContainer/StatsContainer"]
custom_minimum_size = Vector2(50, 15)
layout_mode = 2
theme_override_font_sizes/font_size = 6
text = "提示"

[node name="RestartButton" type="Button" parent="MainContainer/StatsContainer"]
custom_minimum_size = Vector2(50, 15)
layout_mode = 2
theme_override_font_sizes/font_size = 6
text = "重新开始"

class_name GameSelector
extends Control

# 信号
signal game_selected(game_type: GameManager.GameType)
signal selector_closed()

# UI组件引用
@onready var background: ColorRect = $Background
@onready var main_container: VBoxContainer = $MainContainer
@onready var title_label: Label = $MainContainer/TitleLabel
@onready var game_list: VBoxContainer = $MainContainer/ScrollContainer/GameList
@onready var close_button: Button = $MainContainer/CloseButton

var game_manager: GameManager

func _ready():
	# 设置背景半透明
	background.color = Color(0, 0, 0, 0.7)
	
	# 连接关闭按钮
	close_button.pressed.connect(_on_close_button_pressed)
	
	# 初始化游戏管理器
	game_manager = GameManager.new()
	
	# 创建游戏选项
	create_game_options()

# 创建游戏选项按钮
func create_game_options():
	# 清空现有选项
	for child in game_list.get_children():
		child.queue_free()
	
	# 为每种游戏类型创建按钮
	var game_types = game_manager.get_all_game_types()
	for game_type in game_types:
		var game_button = create_game_button(game_type)
		game_list.add_child(game_button)

# 创建单个游戏按钮
func create_game_button(game_type: GameManager.GameType) -> Control:
	var button_container = VBoxContainer.new()
	button_container.add_theme_constant_override("separation", 2)
	
	# 游戏名称按钮
	var game_button = Button.new()
	game_button.text = game_manager.get_game_type_name(game_type)
	game_button.custom_minimum_size = Vector2(200, 30)
	game_button.add_theme_font_size_override("font_size", 12)
	
	# 游戏描述标签
	var description_label = Label.new()
	description_label.text = game_manager.get_game_type_description(game_type)
	description_label.add_theme_font_size_override("font_size", 8)
	description_label.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART
	description_label.custom_minimum_size.x = 200
	description_label.modulate = Color(0.8, 0.8, 0.8)
	
	# 连接按钮信号
	game_button.pressed.connect(_on_game_button_pressed.bind(game_type))
	
	button_container.add_child(game_button)
	button_container.add_child(description_label)
	
	return button_container

# 游戏按钮点击处理
func _on_game_button_pressed(game_type: GameManager.GameType):
	game_selected.emit(game_type)
	hide_selector()

# 关闭按钮点击处理
func _on_close_button_pressed():
	hide_selector()

# 显示选择器
func show_selector():
	show()
	# 播放显示动画
	var tween = create_tween()
	tween.set_parallel(true)
	
	# 背景淡入
	background.modulate = Color.TRANSPARENT
	tween.tween_property(background, "modulate", Color.WHITE, 0.3)
	
	# 主容器从上方滑入
	main_container.position.y = -main_container.size.y
	tween.tween_property(main_container, "position:y", 0, 0.3).set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_OUT)

# 隐藏选择器
func hide_selector():
	var tween = create_tween()
	tween.set_parallel(true)
	
	# 背景淡出
	tween.tween_property(background, "modulate", Color.TRANSPARENT, 0.2)
	
	# 主容器向上滑出
	tween.tween_property(main_container, "position:y", -main_container.size.y, 0.2).set_trans(Tween.TRANS_BACK).set_ease(Tween.EASE_IN)
	
	# 动画结束后隐藏
	tween.tween_callback(hide).set_delay(0.2)
	tween.tween_callback(func(): selector_closed.emit()).set_delay(0.2)

# 处理ESC键关闭
func _input(event):
	if event is InputEventKey and event.pressed:
		if event.keycode == KEY_ESCAPE:
			hide_selector()
			get_viewport().set_input_as_handled()

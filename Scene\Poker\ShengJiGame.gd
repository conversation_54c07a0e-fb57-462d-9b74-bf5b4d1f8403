class_name <PERSON>g<PERSON>iG<PERSON>
extends BaseGame

# 争上游特有信号
signal cards_played(player_name: String, cards: Array[PokerCard])
signal turn_passed(player_name: String)
signal round_ended(winner: String)

# 出牌类型枚举
enum PlayType {
    SINGLE,      # 单张
    PAIR,        # 对子
    TRIPLE,      # 三张
    STRAIGHT,    # 顺子
    BOMB,        # 炸弹（四张相同）
    INVALID      # 无效出牌
}

# 游戏状态
var player_hands: Array[Array] = [[], [], []]  # 3个玩家的手牌
var last_played_cards: Array[PokerCard] = []   # 上次出的牌
var last_played_type: PlayType = PlayType.INVALID
var last_player_index: int = -1
var pass_count: int = 0  # 连续过牌次数
var finished_players: Array[int] = []  # 已出完牌的玩家

func _init():
    super._init()
    # 添加3个玩家
    add_player("玩家")
    add_player("AI1")
    add_player("AI2")

# 获取游戏名称
func get_game_name() -> String:
    return "争上游"

# 获取游戏规则
func get_game_rules() -> String:
    return "快速出完手牌，争取第一名。可以出单张、对子、三张、顺子、炸弹等牌型。"

# 开始新游戏
func start_new_game():
    super.start_new_game()
    
    # 重置游戏状态
    reset_game_state()
    
    # 发牌
    deal_cards()
    
    # 开始游戏
    set_state(GameState.PLAYING)
    current_player_index = 0
    turn_changed.emit(players[0].name)

# 重置游戏状态
func reset_game_state():
    for i in range(3):
        player_hands[i].clear()
        players[i].hand.clear()
    
    last_played_cards.clear()
    last_played_type = PlayType.INVALID
    last_player_index = -1
    pass_count = 0
    finished_players.clear()
    current_player_index = 0

# 发牌（每人17张，剩余1张）
func deal_cards():
    check_deck_size(52)
    
    # 每人发17张牌
    for round in range(17):
        for player_index in range(3):
            var card = deal_card()
            card.is_face_up = (player_index == 0)  # 只有玩家的牌正面朝上
            player_hands[player_index].append(card)
            players[player_index].hand.append(card)
            card_dealt.emit(card, players[player_index].name)
    
    # 对玩家手牌排序
    sort_hand(player_hands[0])

# 对手牌排序
func sort_hand(hand: Array[PokerCard]):
    hand.sort_custom(func(a, b): return a.rank < b.rank)

# 玩家出牌
func player_play_cards(selected_cards: Array[PokerCard]) -> bool:
    if current_player_index != 0 or current_state != GameState.PLAYING:
        return false
    
    # 验证出牌是否有效
    var play_type = get_play_type(selected_cards)
    if not is_valid_play(selected_cards, play_type):
        return false
    
    # 从手牌中移除出的牌
    for card in selected_cards:
        player_hands[0].erase(card)
        players[0].hand.erase(card)
    
    # 更新游戏状态
    last_played_cards = selected_cards.duplicate()
    last_played_type = play_type
    last_player_index = 0
    pass_count = 0
    
    cards_played.emit("玩家", selected_cards)
    
    # 检查是否出完牌
    if player_hands[0].is_empty():
        player_finished(0)
        return true
    
    # 下一个玩家
    next_turn()
    return true

# 玩家过牌
func player_pass() -> bool:
    if current_player_index != 0 or current_state != GameState.PLAYING:
        return false
    
    # 如果是第一次出牌，不能过
    if last_played_cards.is_empty():
        return false
    
    pass_count += 1
    turn_passed.emit("玩家")
    
    # 下一个玩家
    next_turn()
    return true

# 下一个玩家回合
func next_turn():
    # 跳过已完成的玩家
    var attempts = 0
    while attempts < 3:
        current_player_index = (current_player_index + 1) % 3
        if not current_player_index in finished_players:
            break
        attempts += 1
    
    # 检查是否所有其他玩家都过牌了
    if pass_count >= 2:
        # 重新开始新一轮
        last_played_cards.clear()
        last_played_type = PlayType.INVALID
        pass_count = 0
    
    # AI自动出牌
    if current_player_index != 0:
        ai_play(current_player_index)
    else:
        turn_changed.emit("玩家")

# AI出牌
func ai_play(ai_index: int):
    # 简化AI：随机选择出牌或过牌
    var ai_hand = player_hands[ai_index]
    
    if last_played_cards.is_empty():
        # 第一次出牌，出最小的单张
        if not ai_hand.is_empty():
            var card = ai_hand[0]
            ai_hand.erase(card)
            players[ai_index].hand.erase(card)
            
            last_played_cards = [card]
            last_played_type = PlayType.SINGLE
            last_player_index = ai_index
            pass_count = 0
            
            cards_played.emit(players[ai_index].name, [card])
            
            # 检查是否出完牌
            if ai_hand.is_empty():
                player_finished(ai_index)
                return
    else:
        # 随机决定是否过牌
        if randi() % 2 == 0:
            pass_count += 1
            turn_passed.emit(players[ai_index].name)
        else:
            # 尝试出牌（简化：出单张）
            var valid_cards = get_valid_cards_for_ai(ai_hand)
            if not valid_cards.is_empty():
                var card = valid_cards[0]
                ai_hand.erase(card)
                players[ai_index].hand.erase(card)
                
                last_played_cards = [card]
                last_played_type = PlayType.SINGLE
                last_player_index = ai_index
                pass_count = 0
                
                cards_played.emit(players[ai_index].name, [card])
                
                # 检查是否出完牌
                if ai_hand.is_empty():
                    player_finished(ai_index)
                    return
            else:
                pass_count += 1
                turn_passed.emit(players[ai_index].name)
    
    # 下一个玩家
    next_turn()

# 获取AI可以出的牌
func get_valid_cards_for_ai(hand: Array[PokerCard]) -> Array[PokerCard]:
    var valid_cards: Array[PokerCard] = []
    
    if last_played_type == PlayType.SINGLE and not last_played_cards.is_empty():
        var last_rank = last_played_cards[0].rank
        for card in hand:
            if card.rank > last_rank:
                valid_cards.append(card)
    
    return valid_cards

# 玩家完成游戏
func player_finished(player_index: int):
    finished_players.append(player_index)
    
    var result = {}
    if player_index == 0:
        result = {"winner": "玩家", "rank": finished_players.size()}
    else:
        result = {"winner": players[player_index].name, "rank": finished_players.size()}
    
    # 检查游戏是否结束
    if finished_players.size() >= 2:
        # 游戏结束，确定最终排名
        var final_result = {"rankings": []}
        for i in range(finished_players.size()):
            final_result.rankings.append({
                "player": players[finished_players[i]].name,
                "rank": i + 1
            })
        
        # 添加未完成的玩家
        for i in range(3):
            if not i in finished_players:
                final_result.rankings.append({
                    "player": players[i].name,
                    "rank": finished_players.size() + 1
                })
        
        end_game(final_result)
    else:
        round_ended.emit(players[player_index].name)

# 获取出牌类型
func get_play_type(cards: Array[PokerCard]) -> PlayType:
    if cards.is_empty():
        return PlayType.INVALID
    
    var count = cards.size()
    
    match count:
        1:
            return PlayType.SINGLE
        2:
            if cards[0].rank == cards[1].rank:
                return PlayType.PAIR
            else:
                return PlayType.INVALID
        3:
            if cards[0].rank == cards[1].rank and cards[1].rank == cards[2].rank:
                return PlayType.TRIPLE
            else:
                return PlayType.INVALID
        4:
            if (cards[0].rank == cards[1].rank and 
                cards[1].rank == cards[2].rank and 
                cards[2].rank == cards[3].rank):
                return PlayType.BOMB
            else:
                return PlayType.INVALID
        _:
            # 检查是否为顺子（5张或以上）
            if count >= 5:
                var sorted_cards = cards.duplicate()
                sorted_cards.sort_custom(func(a, b): return a.rank < b.rank)
                
                for i in range(1, sorted_cards.size()):
                    if sorted_cards[i].rank != sorted_cards[i-1].rank + 1:
                        return PlayType.INVALID
                
                return PlayType.STRAIGHT
            else:
                return PlayType.INVALID

# 验证出牌是否有效
func is_valid_play(cards: Array[PokerCard], play_type: PlayType) -> bool:
    if play_type == PlayType.INVALID:
        return false
    
    # 如果是第一次出牌，任何有效牌型都可以
    if last_played_cards.is_empty():
        return true
    
    # 炸弹可以压任何牌
    if play_type == PlayType.BOMB:
        return true
    
    # 必须是相同类型的牌
    if play_type != last_played_type:
        return false
    
    # 比较牌的大小
    match play_type:
        PlayType.SINGLE:
            return cards[0].rank > last_played_cards[0].rank
        PlayType.PAIR:
            return cards[0].rank > last_played_cards[0].rank
        PlayType.TRIPLE:
            return cards[0].rank > last_played_cards[0].rank
        PlayType.STRAIGHT:
            return cards[0].rank > last_played_cards[0].rank
        _:
            return false

# 获取玩家手牌
func get_player_hand() -> Array[PokerCard]:
    return player_hands[0]

# 获取可选择的牌
func get_selectable_cards() -> Array[PokerCard]:
    if current_player_index != 0:
        return []
    
    return player_hands[0]

class_name GameManager
extends RefCounted

# 游戏类型枚举
enum GameType {
	# 基础扑克游戏
	BLACKJACK,    # 21点
	TEXAS_HOLDEM, # 德州扑克
	SHENG_JI,     # 争上游
	DOU_DI_ZHU,   # 斗地主

	# 经典扑克变种
	BIG_TWO,      # 锄大地/十三张/大老二
	OMAHA,        # 奥马哈
	SEVEN_STUD,   # 七张梭哈
	FIVE_DRAW,    # 五张抽
	RAZZ,         # Razz（低牌）
	DEUCE_SEVEN,  # 2-7三次换

	# 花色游戏
	SPADES,       # 拱猪
	HEARTS,       # 红心大战

	# 休闲游戏
	CRAZY_EIGHTS, # 疯狂八
	GO_FISH,      # 钓鱼
	MEMORY,       # 记忆翻牌
	WAR,          # 比大小
	OLD_MAID      # 抽乌龟
}

# 信号
signal game_type_changed(new_type: GameType)
signal game_started()
signal game_ended()

var current_game_type: GameType = GameType.BLACKJACK
var current_game: RefCounted = null

# 获取游戏类型的显示名称
func get_game_type_name(type: GameType) -> String:
	match type:
		# 基础扑克游戏
		GameType.BLACKJACK:
			return "21点"
		GameType.TEXAS_HOLDEM:
			return "德州扑克"
		GameType.SHENG_JI:
			return "争上游"
		GameType.DOU_DI_ZHU:
			return "斗地主"

		# 经典扑克变种
		GameType.BIG_TWO:
			return "锄大地"
		GameType.OMAHA:
			return "奥马哈"
		GameType.SEVEN_STUD:
			return "七张梭哈"
		GameType.FIVE_DRAW:
			return "五张抽"
		GameType.RAZZ:
			return "Razz低牌"
		GameType.DEUCE_SEVEN:
			return "2-7三次换"

		# 花色游戏
		GameType.SPADES:
			return "拱猪"
		GameType.HEARTS:
			return "红心大战"

		# 休闲游戏
		GameType.CRAZY_EIGHTS:
			return "疯狂八"
		GameType.GO_FISH:
			return "钓鱼"
		GameType.MEMORY:
			return "记忆翻牌"
		GameType.WAR:
			return "比大小"
		GameType.OLD_MAID:
			return "抽乌龟"
		_:
			return "未知游戏"

# 获取游戏类型的描述
func get_game_type_description(type: GameType) -> String:
	match type:
		# 基础扑克游戏
		GameType.BLACKJACK:
			return "经典21点游戏，目标是让手牌总和接近21点但不超过"
		GameType.TEXAS_HOLDEM:
			return "德州扑克，使用2张底牌和5张公共牌组成最佳牌型"
		GameType.SHENG_JI:
			return "争上游，快速出完手牌，争取第一名"
		GameType.DOU_DI_ZHU:
			return "斗地主，地主对抗农民的经典扑克游戏"

		# 经典扑克变种
		GameType.BIG_TWO:
			return "锄大地，出完13张牌为胜，2最大，花色有等级"
		GameType.OMAHA:
			return "奥马哈扑克，4张底牌必须用2张，与3张公共牌组合"
		GameType.SEVEN_STUD:
			return "七张梭哈，每人7张牌，部分明牌部分暗牌"
		GameType.FIVE_DRAW:
			return "五张抽牌，可换牌改善手牌，经典扑克玩法"
		GameType.RAZZ:
			return "Razz低牌，目标是组成最低的牌型，A最小"
		GameType.DEUCE_SEVEN:
			return "2-7三次换牌，三轮换牌机会，最低牌型获胜"

		# 花色游戏
		GameType.SPADES:
			return "拱猪，避免得到分数牌，黑桃Q是猪头"
		GameType.HEARTS:
			return "红心大战，避免得到红心和黑桃Q"

		# 休闲游戏
		GameType.CRAZY_EIGHTS:
			return "疯狂八，出完手牌获胜，8可以改变花色"
		GameType.GO_FISH:
			return "钓鱼游戏，收集四张相同点数的牌"
		GameType.MEMORY:
			return "记忆翻牌，翻开相同的牌配对"
		GameType.WAR:
			return "比大小，简单的牌面大小比较游戏"
		GameType.OLD_MAID:
			return "抽乌龟，避免最后剩下老K"
		_:
			return ""

# 切换游戏类型
func switch_game_type(new_type: GameType):
	if current_game_type == new_type:
		return
	
	# 结束当前游戏
	if current_game:
		current_game = null
	
	current_game_type = new_type
	game_type_changed.emit(new_type)

# 创建指定类型的游戏实例
func create_game_instance(type: GameType) -> RefCounted:
	match type:
		# 基础扑克游戏
		GameType.BLACKJACK:
			return BlackjackGame.new()
		GameType.TEXAS_HOLDEM:
			return TexasHoldemGame.new()
		GameType.SHENG_JI:
			return ShengJiGame.new()
		GameType.DOU_DI_ZHU:
			return DouDiZhuGame.new()

		# 经典扑克变种
		GameType.BIG_TWO:
			return BigTwoGame.new()
		GameType.OMAHA:
			return OmahaGame.new()
		GameType.SEVEN_STUD:
			return SevenStudGame.new()
		GameType.FIVE_DRAW:
			return FiveDrawGame.new()
		GameType.RAZZ:
			return RazzGame.new()
		GameType.DEUCE_SEVEN:
			return DeuceSevenGame.new()

		# 花色游戏
		GameType.SPADES:
			return SpadesGame.new()
		GameType.HEARTS:
			return HeartsGame.new()

		# 休闲游戏
		GameType.CRAZY_EIGHTS:
			return CrazyEightsGame.new()
		GameType.GO_FISH:
			return GoFishGame.new()
		GameType.MEMORY:
			return MemoryGame.new()
		GameType.WAR:
			return WarGame.new()
		GameType.OLD_MAID:
			return OldMaidGame.new()
		_:
			return null

# 获取当前游戏类型
func get_current_game_type() -> GameType:
	return current_game_type

# 获取当前游戏实例
func get_current_game() -> RefCounted:
	return current_game

# 设置当前游戏实例
func set_current_game(game: RefCounted):
	current_game = game

# 获取所有可用的游戏类型
func get_all_game_types() -> Array[GameType]:
	return [
		# 基础扑克游戏
		GameType.BLACKJACK,
		GameType.TEXAS_HOLDEM,
		GameType.SHENG_JI,
		GameType.DOU_DI_ZHU,

		# 经典扑克变种
		GameType.BIG_TWO,
		GameType.OMAHA,
		GameType.SEVEN_STUD,
		GameType.FIVE_DRAW,
		GameType.RAZZ,
		GameType.DEUCE_SEVEN,

		# 花色游戏
		GameType.SPADES,
		GameType.HEARTS,

		# 休闲游戏
		GameType.CRAZY_EIGHTS,
		GameType.GO_FISH,
		GameType.MEMORY,
		GameType.WAR,
		GameType.OLD_MAID
	]

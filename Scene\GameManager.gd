class_name GameManager
extends RefCounted

# 游戏类型枚举
enum GameType {
	BLACKJACK,    # 21点
	TEXAS_HOLDEM, # 德州扑克
	SHENG_JI,     # 争上游
	DOU_DI_ZHU    # 斗地主
}

# 信号
signal game_type_changed(new_type: GameType)
signal game_started()
signal game_ended()

var current_game_type: GameType = GameType.BLACKJACK
var current_game: RefCounted = null

# 获取游戏类型的显示名称
func get_game_type_name(type: GameType) -> String:
	match type:
		GameType.BLACKJACK:
			return "21点"
		GameType.TEXAS_HOLDEM:
			return "德州扑克"
		GameType.SHENG_JI:
			return "争上游"
		GameType.DOU_DI_ZHU:
			return "斗地主"
		_:
			return "未知游戏"

# 获取游戏类型的描述
func get_game_type_description(type: GameType) -> String:
	match type:
		GameType.BLACKJACK:
			return "经典21点游戏，目标是让手牌总和接近21点但不超过"
		GameType.TEXAS_HOLDEM:
			return "德州扑克，使用2张底牌和5张公共牌组成最佳牌型"
		GameType.SHENG_JI:
			return "争上游，快速出完手牌，争取第一名"
		GameType.DOU_DI_ZHU:
			return "斗地主，地主对抗农民的经典扑克游戏"
		_:
			return ""

# 切换游戏类型
func switch_game_type(new_type: GameType):
	if current_game_type == new_type:
		return
	
	# 结束当前游戏
	if current_game:
		current_game = null
	
	current_game_type = new_type
	game_type_changed.emit(new_type)

# 创建指定类型的游戏实例
func create_game_instance(type: GameType) -> RefCounted:
	match type:
		GameType.BLACKJACK:
			return BlackjackGame.new()
		GameType.TEXAS_HOLDEM:
			return TexasHoldemGame.new()
		GameType.SHENG_JI:
			return ShengJiGame.new()
		GameType.DOU_DI_ZHU:
			return DouDiZhuGame.new()
		_:
			return null

# 获取当前游戏类型
func get_current_game_type() -> GameType:
	return current_game_type

# 获取当前游戏实例
func get_current_game() -> RefCounted:
	return current_game

# 设置当前游戏实例
func set_current_game(game: RefCounted):
	current_game = game

# 获取所有可用的游戏类型
func get_all_game_types() -> Array[GameType]:
	return [
		GameType.BLACKJACK,
		GameType.TEXAS_HOLDEM,
		GameType.SHENG_JI,
		GameType.DOU_DI_ZHU
	]

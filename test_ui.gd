extends Control

var game_manager: GameManager
var current_game_type: GameManager.GameType = GameManager.GameType.BLACKJACK

func _ready():
	# 设置窗口大小
	size = Vector2(800, 100)
	
	# 创建游戏管理器
	game_manager = GameManager.new()
	
	# 创建测试按钮
	create_test_buttons()
	
	print("测试UI已启动")

func create_test_buttons():
	var container = HBoxContainer.new()
	container.set_anchors_and_offsets_preset(Control.PRESET_CENTER)
	add_child(container)
	
	# 创建四个游戏切换按钮
	var games = [
		GameManager.GameType.BLACKJACK,
		GameManager.GameType.TEXAS_HOLDEM,
		GameManager.GameType.SHENG_JI,
		GameManager.GameType.DOU_DI_ZHU
	]
	
	for game_type in games:
		var button = Button.new()
		button.text = game_manager.get_game_type_name(game_type)
		button.custom_minimum_size = Vector2(120, 40)
		button.add_theme_font_size_override("font_size", 14)
		button.pressed.connect(_on_game_button_pressed.bind(game_type))
		container.add_child(button)
	
	# 添加当前游戏显示标签
	var label = Label.new()
	label.text = "当前游戏: " + game_manager.get_game_type_name(current_game_type)
	label.add_theme_font_size_override("font_size", 16)
	label.set_anchors_and_offsets_preset(Control.PRESET_TOP_CENTER)
	label.position.y = 10
	add_child(label)

func _on_game_button_pressed(game_type: GameManager.GameType):
	current_game_type = game_type
	print("切换到游戏: ", game_manager.get_game_type_name(game_type))
	
	# 更新标签
	var label = get_children().filter(func(child): return child is Label)[0] as Label
	if label:
		label.text = "当前游戏: " + game_manager.get_game_type_name(game_type)

func _input(event):
	if event is InputEventKey and event.pressed:
		match event.keycode:
			KEY_1:
				_on_game_button_pressed(GameManager.GameType.BLACKJACK)
			KEY_2:
				_on_game_button_pressed(GameManager.GameType.TEXAS_HOLDEM)
			KEY_3:
				_on_game_button_pressed(GameManager.GameType.SHENG_JI)
			KEY_4:
				_on_game_button_pressed(GameManager.GameType.DOU_DI_ZHU)

class_name OmahaGame
extends BaseGame

# 奥马哈扑克特有信号
signal community_cards_dealt(cards: Array)
signal betting_round_started(round_name: String)
signal player_action(player_name: String, action: String, amount: int)

# 奥马哈扑克游戏阶段
enum OmahaPhase {
	PRE_FLOP,   # 翻牌前
	FLOP,       # 翻牌
	TURN,       # 转牌
	RIVER,      # 河牌
	SHOWDOWN    # 摊牌
}

# 牌型枚举（与德州扑克相同）
enum HandRank {
	HIGH_CARD,      # 高牌
	PAIR,           # 一对
	TWO_PAIR,       # 两对
	THREE_KIND,     # 三条
	STRAIGHT,       # 顺子
	FLUSH,          # 同花
	FULL_HOUSE,     # 葫芦
	FOUR_KIND,      # 四条
	STRAIGHT_FLUSH, # 同花顺
	ROYAL_FLUSH     # 皇家同花顺
}

# 游戏状态
var current_phase: OmahaPhase = OmahaPhase.PRE_FLOP
var community_cards: Array = []
var pot: int = 0  # 底池
var current_bet: int = 0  # 当前下注额

# 玩家数据（奥马哈每人4张底牌）
var player_hole_cards: Array = []  # 玩家底牌（4张）
var ai_hole_cards: Array = []      # AI底牌（4张）

func _init():
	super._init()
	# 添加玩家和AI
	add_player("玩家", {"chips": 1000, "current_bet": 0, "folded": false})
	add_player("AI", {"chips": 1000, "current_bet": 0, "folded": false})

# 获取游戏名称
func get_game_name() -> String:
	return "奥马哈扑克"

# 获取游戏规则
func get_game_rules() -> String:
	return "每人4张底牌，必须用其中2张与公共牌中的3张组成最佳5张牌。"

# 开始新游戏
func start_new_game():
	super.start_new_game()
	
	# 重置游戏状态
	reset_game_state()
	
	# 发底牌（每人4张）
	deal_hole_cards()
	
	# 开始翻牌前阶段
	current_phase = OmahaPhase.PRE_FLOP
	set_state(GameState.PLAYING)
	betting_round_started.emit("翻牌前")

# 重置游戏状态
func reset_game_state():
	community_cards.clear()
	player_hole_cards.clear()
	ai_hole_cards.clear()
	pot = 0
	current_bet = 0
	current_phase = OmahaPhase.PRE_FLOP
	
	# 重置玩家状态
	for player in players:
		player.data.current_bet = 0
		player.data.folded = false
		player.hand.clear()

# 发底牌（每人4张）
func deal_hole_cards():
	# 每个玩家发4张底牌
	for i in range(4):
		# 玩家底牌
		var player_card = deal_card()
		player_card.is_face_up = true
		player_hole_cards.append(player_card)
		players[0].hand.append(player_card)
		card_dealt.emit(player_card, "玩家")
		
		# AI底牌（背面）
		var ai_card = deal_card()
		ai_card.is_face_up = false
		ai_hole_cards.append(ai_card)
		players[1].hand.append(ai_card)
		card_dealt.emit(ai_card, "AI")

# 进入下一阶段
func next_phase():
	match current_phase:
		OmahaPhase.PRE_FLOP:
			deal_flop()
		OmahaPhase.FLOP:
			deal_turn()
		OmahaPhase.TURN:
			deal_river()
		OmahaPhase.RIVER:
			showdown()

# 发翻牌（3张公共牌）
func deal_flop():
	current_phase = OmahaPhase.FLOP
	
	# 烧掉一张牌
	deal_card()
	
	# 发3张翻牌
	for i in range(3):
		var card = deal_card()
		card.is_face_up = true
		community_cards.append(card)
		card_dealt.emit(card, "公共牌")
	
	community_cards_dealt.emit(community_cards)
	betting_round_started.emit("翻牌")

# 发转牌（第4张公共牌）
func deal_turn():
	current_phase = OmahaPhase.TURN
	
	# 烧掉一张牌
	deal_card()
	
	# 发转牌
	var card = deal_card()
	card.is_face_up = true
	community_cards.append(card)
	card_dealt.emit(card, "公共牌")
	
	community_cards_dealt.emit(community_cards)
	betting_round_started.emit("转牌")

# 发河牌（第5张公共牌）
func deal_river():
	current_phase = OmahaPhase.RIVER
	
	# 烧掉一张牌
	deal_card()
	
	# 发河牌
	var card = deal_card()
	card.is_face_up = true
	community_cards.append(card)
	card_dealt.emit(card, "公共牌")
	
	community_cards_dealt.emit(community_cards)
	betting_round_started.emit("河牌")

# 摊牌
func showdown():
	current_phase = OmahaPhase.SHOWDOWN
	
	# 翻开AI的底牌
	for card in ai_hole_cards:
		card.is_face_up = true
	
	# 计算最佳牌型（奥马哈规则：必须用2张底牌+3张公共牌）
	var player_best = get_best_omaha_hand(player_hole_cards, community_cards)
	var ai_best = get_best_omaha_hand(ai_hole_cards, community_cards)
	
	# 比较牌型
	var result = compare_hands(player_best, ai_best)
	
	var game_result = {}
	if result > 0:
		game_result = {"winner": "玩家", "reason": "牌型更好"}
	elif result < 0:
		game_result = {"winner": "AI", "reason": "牌型更好"}
	else:
		game_result = {"winner": "平局", "reason": "牌型相同"}
	
	game_result["player_hand"] = player_best
	game_result["ai_hand"] = ai_best
	game_result["pot"] = pot
	
	end_game(game_result)

# 获取奥马哈最佳手牌（必须用2张底牌+3张公共牌）
func get_best_omaha_hand(hole_cards: Array, community: Array) -> Array:
	var best_hand: Array = []
	var best_rank = -1
	
	# 遍历所有可能的2张底牌组合
	for i in range(hole_cards.size()):
		for j in range(i + 1, hole_cards.size()):
			var hole_combo = [hole_cards[i], hole_cards[j]]
			
			# 遍历所有可能的3张公共牌组合
			for x in range(community.size()):
				for y in range(x + 1, community.size()):
					for z in range(y + 1, community.size()):
						var community_combo = [community[x], community[y], community[z]]
						var hand = hole_combo + community_combo
						
						var rank = evaluate_hand_rank(hand)
						if rank > best_rank:
							best_rank = rank
							best_hand = hand
	
	return best_hand

# 评估牌型等级
func evaluate_hand_rank(hand: Array) -> HandRank:
	# 简化实现：只检查基本牌型
	var ranks = {}
	var suits = {}
	
	for card in hand:
		var rank = card.rank
		var suit = card.suit
		
		if rank in ranks:
			ranks[rank] += 1
		else:
			ranks[rank] = 1
			
		if suit in suits:
			suits[suit] += 1
		else:
			suits[suit] = 1
	
	# 检查对子
	var pairs = 0
	var three_kind = false
	var four_kind = false
	
	for count in ranks.values():
		if count == 2:
			pairs += 1
		elif count == 3:
			three_kind = true
		elif count == 4:
			four_kind = true
	
	# 检查同花
	var is_flush = false
	for count in suits.values():
		if count >= 5:
			is_flush = true
			break
	
	# 返回牌型
	if four_kind:
		return HandRank.FOUR_KIND
	elif three_kind and pairs > 0:
		return HandRank.FULL_HOUSE
	elif is_flush:
		return HandRank.FLUSH
	elif three_kind:
		return HandRank.THREE_KIND
	elif pairs >= 2:
		return HandRank.TWO_PAIR
	elif pairs == 1:
		return HandRank.PAIR
	else:
		return HandRank.HIGH_CARD

# 比较两手牌的大小
func compare_hands(hand1: Array, hand2: Array) -> int:
	var rank1 = evaluate_hand_rank(hand1)
	var rank2 = evaluate_hand_rank(hand2)
	
	if rank1 > rank2:
		return 1
	elif rank1 < rank2:
		return -1
	else:
		return 0

# 玩家动作
func player_call():
	if current_state != GameState.PLAYING:
		return
	
	var call_amount = current_bet - players[0].data.current_bet
	players[0].data.current_bet = current_bet
	pot += call_amount
	
	# 根据金额决定动作名称
	var action_name = "看牌" if call_amount == 0 else "跟注"
	player_action.emit("玩家", action_name, call_amount)
	
	# AI自动行动
	ai_action()

func player_raise(amount: int):
	if current_state != GameState.PLAYING:
		return
	
	current_bet += amount
	players[0].data.current_bet = current_bet
	pot += amount
	player_action.emit("玩家", "加注", amount)
	
	# AI自动行动
	ai_action()

func player_fold():
	if current_state != GameState.PLAYING:
		return
	
	players[0].data.folded = true
	player_action.emit("玩家", "弃牌", 0)
	
	# AI获胜
	end_game({"winner": "AI", "reason": "玩家弃牌", "pot": pot})

# AI自动行动（简化实现）
func ai_action():
	# 简单AI：随机选择行动
	var action = randi() % 3
	
	match action:
		0: # 跟注/看牌
			var call_amount = current_bet - players[1].data.current_bet
			players[1].data.current_bet = current_bet
			pot += call_amount
			var action_name = "看牌" if call_amount == 0 else "跟注"
			player_action.emit("AI", action_name, call_amount)
		1: # 加注
			var raise_amount = 50
			current_bet += raise_amount
			players[1].data.current_bet = current_bet
			pot += raise_amount
			player_action.emit("AI", "加注", raise_amount)
		2: # 弃牌（降低弃牌概率）
			if randi() % 4 == 0:  # 只有25%概率弃牌
				players[1].data.folded = true
				player_action.emit("AI", "弃牌", 0)
				end_game({"winner": "玩家", "reason": "AI弃牌", "pot": pot})
				return
			else:
				# 改为跟注/看牌
				var call_amount = current_bet - players[1].data.current_bet
				players[1].data.current_bet = current_bet
				pot += call_amount
				var action_name = "看牌" if call_amount == 0 else "跟注"
				player_action.emit("AI", action_name, call_amount)
	
	# 进入下一阶段
	next_phase()

# 获取当前阶段名称
func get_phase_name() -> String:
	match current_phase:
		OmahaPhase.PRE_FLOP:
			return "翻牌前"
		OmahaPhase.FLOP:
			return "翻牌"
		OmahaPhase.TURN:
			return "转牌"
		OmahaPhase.RIVER:
			return "河牌"
		OmahaPhase.SHOWDOWN:
			return "摊牌"
		_:
			return "未知阶段"

# 获取玩家底牌
func get_player_hole_cards() -> Array:
	return player_hole_cards

# 获取AI底牌
func get_ai_hole_cards() -> Array:
	return ai_hole_cards

# 获取公共牌
func get_community_cards() -> Array:
	return community_cards

class_name <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
extends BaseGame

# 斗地主特有信号
signal landlord_selected(player_name: String)
signal bidding_started()
signal cards_played(player_name: String, cards: Array[PokerCard])
signal turn_passed(player_name: String)

# 出牌类型枚举
enum PlayType {
    SINGLE,         # 单张
    PAIR,           # 对子
    TRIPLE,         # 三张
    TRIPLE_SINGLE,  # 三带一
    TRIPLE_PAIR,    # 三带二
    STRAIGHT,       # 顺子
    PAIR_STRAIGHT,  # 连对
    PLANE,          # 飞机
    BOMB,           # 炸弹
    ROCKET,         # 火箭（双王）
    INVALID         # 无效
}

# 游戏阶段
enum GamePhase {
    BIDDING,    # 叫地主
    PLAYING     # 出牌
}

# 游戏状态
var current_phase: GamePhase = GamePhase.BIDDING
var landlord_index: int = -1  # 地主索引
var player_hands: Array[Array] = [[], [], []]  # 3个玩家手牌
var landlord_cards: Array[PokerCard] = []  # 地主牌（3张）
var last_played_cards: Array[PokerCard] = []
var last_played_type: PlayType = PlayType.INVALID
var last_player_index: int = -1
var pass_count: int = 0
var bid_count: int = 0  # 叫地主次数

func _init():
    super._init()
    # 添加3个玩家
    add_player("玩家", {"is_landlord": false, "bid": false})
    add_player("AI1", {"is_landlord": false, "bid": false})
    add_player("AI2", {"is_landlord": false, "bid": false})

# 获取游戏名称
func get_game_name() -> String:
    return "斗地主"

# 获取游戏规则
func get_game_rules() -> String:
    return "地主对抗农民的经典游戏。先叫地主，然后地主先出牌，目标是最先出完手牌。"

# 开始新游戏
func start_new_game():
    super.start_new_game()
    
    # 重置游戏状态
    reset_game_state()
    
    # 发牌
    deal_cards()
    
    # 开始叫地主阶段
    current_phase = GamePhase.BIDDING
    set_state(GameState.PLAYING)
    bidding_started.emit()
    
    # 从玩家开始叫地主
    current_player_index = 0
    turn_changed.emit("叫地主阶段")

# 重置游戏状态
func reset_game_state():
    current_phase = GamePhase.BIDDING
    landlord_index = -1
    
    for i in range(3):
        player_hands[i].clear()
        players[i].hand.clear()
        players[i].data.is_landlord = false
        players[i].data.bid = false
    
    landlord_cards.clear()
    last_played_cards.clear()
    last_played_type = PlayType.INVALID
    last_player_index = -1
    pass_count = 0
    bid_count = 0
    current_player_index = 0

# 发牌（每人17张，剩余3张作为地主牌）
func deal_cards():
    # 添加大小王
    var joker1 = PokerCard.new(PokerCard.Suit.CLUBS, PokerCard.Rank.JOKER)
    var joker2 = PokerCard.new(PokerCard.Suit.SPADES, PokerCard.Rank.JOKER)
    deck.append(joker1)
    deck.append(joker2)
    
    shuffle_deck()
    
    # 每人发17张牌
    for round in range(17):
        for player_index in range(3):
            var card = deal_card()
            card.is_face_up = (player_index == 0)  # 只有玩家的牌正面朝上
            player_hands[player_index].append(card)
            players[player_index].hand.append(card)
            card_dealt.emit(card, players[player_index].name)
    
    # 剩余3张作为地主牌（背面朝上）
    for i in range(3):
        var card = deal_card()
        card.is_face_up = false
        landlord_cards.append(card)
        card_dealt.emit(card, "地主牌")
    
    # 对玩家手牌排序
    sort_hand(player_hands[0])

# 对手牌排序（斗地主排序：3最小，2最大，小王，大王）
func sort_hand(hand: Array[PokerCard]):
    hand.sort_custom(func(a, b): return get_card_order(a) < get_card_order(b))

# 获取牌的排序值
func get_card_order(card: PokerCard) -> int:
    if card.rank == PokerCard.Rank.JOKER:
        if card.suit == PokerCard.Suit.CLUBS:
            return 16  # 小王
        else:
            return 17  # 大王
    elif card.rank == PokerCard.Rank.TWO:
        return 15  # 2最大
    elif card.rank == PokerCard.Rank.ACE:
        return 14  # A第二大
    elif card.rank == PokerCard.Rank.THREE:
        return 3   # 3最小
    else:
        return card.rank

# 玩家叫地主
func player_bid_landlord() -> bool:
    if current_phase != GamePhase.BIDDING or current_player_index != 0:
        return false
    
    players[0].data.bid = true
    bid_count += 1
    
    # 成为地主
    become_landlord(0)
    return true

# 玩家不叫
func player_pass_bid() -> bool:
    if current_phase != GamePhase.BIDDING or current_player_index != 0:
        return false
    
    # 下一个玩家叫地主
    next_bidding_turn()
    return true

# 下一个叫地主回合
func next_bidding_turn():
    current_player_index = (current_player_index + 1) % 3
    
    # AI自动叫地主（简化：随机决定）
    if current_player_index != 0:
        if randi() % 3 == 0:  # 1/3概率叫地主
            players[current_player_index].data.bid = true
            bid_count += 1
            become_landlord(current_player_index)
        else:
            # 继续下一个
            if bid_count == 0 and current_player_index == 2:
                # 如果没人叫地主，重新发牌
                start_new_game()
            else:
                next_bidding_turn()

# 成为地主
func become_landlord(player_index: int):
    landlord_index = player_index
    players[player_index].data.is_landlord = true
    
    # 地主获得3张底牌
    for card in landlord_cards:
        card.is_face_up = (player_index == 0)
        player_hands[player_index].append(card)
        players[player_index].hand.append(card)
    
    # 重新排序地主手牌
    if player_index == 0:
        sort_hand(player_hands[0])
    
    landlord_selected.emit(players[player_index].name)
    
    # 开始出牌阶段
    current_phase = GamePhase.PLAYING
    current_player_index = landlord_index
    last_played_cards.clear()
    last_played_type = PlayType.INVALID
    pass_count = 0
    
    if landlord_index == 0:
        turn_changed.emit("玩家")
    else:
        ai_play(landlord_index)

# 玩家出牌
func player_play_cards(selected_cards: Array[PokerCard]) -> bool:
    if current_phase != GamePhase.PLAYING or current_player_index != 0:
        return false
    
    # 验证出牌
    var play_type = get_play_type(selected_cards)
    if not is_valid_play(selected_cards, play_type):
        return false
    
    # 从手牌中移除
    for card in selected_cards:
        player_hands[0].erase(card)
        players[0].hand.erase(card)
    
    # 更新状态
    last_played_cards = selected_cards.duplicate()
    last_played_type = play_type
    last_player_index = 0
    pass_count = 0
    
    cards_played.emit("玩家", selected_cards)
    
    # 检查是否获胜
    if player_hands[0].is_empty():
        check_game_end()
        return true
    
    # 下一个玩家
    next_turn()
    return true

# 玩家过牌
func player_pass() -> bool:
    if current_phase != GamePhase.PLAYING or current_player_index != 0:
        return false
    
    if last_played_cards.is_empty():
        return false  # 第一次出牌不能过
    
    pass_count += 1
    turn_passed.emit("玩家")
    
    next_turn()
    return true

# 下一个玩家回合
func next_turn():
    current_player_index = (current_player_index + 1) % 3
    
    # 检查是否所有其他玩家都过牌
    if pass_count >= 2:
        last_played_cards.clear()
        last_played_type = PlayType.INVALID
        pass_count = 0
    
    # AI自动出牌
    if current_player_index != 0:
        ai_play(current_player_index)
    else:
        turn_changed.emit("玩家")

# AI出牌
func ai_play(ai_index: int):
    var ai_hand = player_hands[ai_index]
    
    if last_played_cards.is_empty():
        # 第一次出牌，出最小的单张
        if not ai_hand.is_empty():
            var card = ai_hand[0]
            ai_hand.erase(card)
            players[ai_index].hand.erase(card)
            
            last_played_cards = [card]
            last_played_type = PlayType.SINGLE
            last_player_index = ai_index
            pass_count = 0
            
            cards_played.emit(players[ai_index].name, [card])
            
            if ai_hand.is_empty():
                check_game_end()
                return
    else:
        # 随机决定出牌或过牌
        if randi() % 2 == 0:
            pass_count += 1
            turn_passed.emit(players[ai_index].name)
        else:
            # 简化：尝试出单张
            var valid_cards = get_valid_single_cards(ai_hand)
            if not valid_cards.is_empty():
                var card = valid_cards[0]
                ai_hand.erase(card)
                players[ai_index].hand.erase(card)
                
                last_played_cards = [card]
                last_played_type = PlayType.SINGLE
                last_player_index = ai_index
                pass_count = 0
                
                cards_played.emit(players[ai_index].name, [card])
                
                if ai_hand.is_empty():
                    check_game_end()
                    return
            else:
                pass_count += 1
                turn_passed.emit(players[ai_index].name)
    
    next_turn()

# 获取AI可出的单张牌
func get_valid_single_cards(hand: Array[PokerCard]) -> Array[PokerCard]:
    var valid_cards: Array[PokerCard] = []
    
    if last_played_type == PlayType.SINGLE and not last_played_cards.is_empty():
        var last_order = get_card_order(last_played_cards[0])
        for card in hand:
            if get_card_order(card) > last_order:
                valid_cards.append(card)
    
    return valid_cards

# 检查游戏结束
func check_game_end():
    var winner_index = -1
    for i in range(3):
        if player_hands[i].is_empty():
            winner_index = i
            break
    
    if winner_index != -1:
        var result = {}
        if winner_index == landlord_index:
            result = {"winner": "地主", "player": players[winner_index].name}
        else:
            result = {"winner": "农民", "player": players[winner_index].name}
        
        end_game(result)

# 获取出牌类型（简化版本）
func get_play_type(cards: Array[PokerCard]) -> PlayType:
    if cards.is_empty():
        return PlayType.INVALID
    
    var count = cards.size()
    
    match count:
        1:
            return PlayType.SINGLE
        2:
            if cards[0].rank == cards[1].rank:
                return PlayType.PAIR
            elif ((cards[0].rank == PokerCard.Rank.JOKER and cards[1].rank == PokerCard.Rank.JOKER) and
                  cards[0].suit != cards[1].suit):
                return PlayType.ROCKET  # 双王
            else:
                return PlayType.INVALID
        3:
            if cards[0].rank == cards[1].rank and cards[1].rank == cards[2].rank:
                return PlayType.TRIPLE
            else:
                return PlayType.INVALID
        4:
            if (cards[0].rank == cards[1].rank and 
                cards[1].rank == cards[2].rank and 
                cards[2].rank == cards[3].rank):
                return PlayType.BOMB
            else:
                return PlayType.INVALID
        _:
            return PlayType.INVALID

# 验证出牌是否有效
func is_valid_play(cards: Array[PokerCard], play_type: PlayType) -> bool:
    if play_type == PlayType.INVALID:
        return false
    
    if last_played_cards.is_empty():
        return true
    
    # 火箭最大
    if play_type == PlayType.ROCKET:
        return true
    
    # 炸弹可以压除火箭外的任何牌
    if play_type == PlayType.BOMB and last_played_type != PlayType.ROCKET:
        return true
    
    # 必须是相同类型
    if play_type != last_played_type:
        return false
    
    # 比较大小
    match play_type:
        PlayType.SINGLE:
            return get_card_order(cards[0]) > get_card_order(last_played_cards[0])
        PlayType.PAIR:
            return get_card_order(cards[0]) > get_card_order(last_played_cards[0])
        PlayType.TRIPLE:
            return get_card_order(cards[0]) > get_card_order(last_played_cards[0])
        PlayType.BOMB:
            return get_card_order(cards[0]) > get_card_order(last_played_cards[0])
        _:
            return false

# 获取玩家手牌
func get_player_hand() -> Array[PokerCard]:
    return player_hands[0]

# 检查是否为地主
func is_landlord(player_index: int) -> bool:
    return player_index == landlord_index

# 获取当前阶段
func get_current_phase() -> GamePhase:
    return current_phase

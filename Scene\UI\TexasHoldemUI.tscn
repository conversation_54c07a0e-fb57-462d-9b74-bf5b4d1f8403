[gd_scene load_steps=2 format=3 uid="uid://d8xvn4qxqxqxr"]

[ext_resource type="Script" path="res://Scene/UI/TexasHoldemUI.gd" id="1_texas_holdem_ui"]

[node name="TexasHoldemUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_texas_holdem_ui")

[node name="MainContainer" type="HBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme_override_constants/separation = 3

[node name="AIArea" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 1

[node name="AILabel" type="Label" parent="MainContainer/AIArea"]
layout_mode = 2
theme_override_font_sizes/font_size = 6
text = "AI"
horizontal_alignment = 1

[node name="HoleCardsContainer" type="HBoxContainer" parent="MainContainer/AIArea"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/separation = 1
alignment = 1

[node name="CenterArea" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 1

[node name="PhaseLabel" type="Label" parent="MainContainer/CenterArea"]
layout_mode = 2
theme_override_font_sizes/font_size = 6
text = "阶段: 等待"
horizontal_alignment = 1

[node name="CommunityContainer" type="HBoxContainer" parent="MainContainer/CenterArea"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/separation = 1
alignment = 1

[node name="PotLabel" type="Label" parent="MainContainer/CenterArea"]
layout_mode = 2
theme_override_font_sizes/font_size = 6
text = "底池: 0"
horizontal_alignment = 1

[node name="GameStatusLabel" type="Label" parent="MainContainer/CenterArea"]
layout_mode = 2
theme_override_font_sizes/font_size = 6
text = "点击新游戏开始"
horizontal_alignment = 1

[node name="ButtonContainer" type="HBoxContainer" parent="MainContainer/CenterArea"]
layout_mode = 2
theme_override_constants/separation = 2
alignment = 1

[node name="CallButton" type="Button" parent="MainContainer/CenterArea/ButtonContainer"]
custom_minimum_size = Vector2(25, 12)
layout_mode = 2
theme_override_font_sizes/font_size = 5
disabled = true
text = "跟注"

[node name="RaiseButton" type="Button" parent="MainContainer/CenterArea/ButtonContainer"]
custom_minimum_size = Vector2(25, 12)
layout_mode = 2
theme_override_font_sizes/font_size = 5
disabled = true
text = "加注"

[node name="FoldButton" type="Button" parent="MainContainer/CenterArea/ButtonContainer"]
custom_minimum_size = Vector2(25, 12)
layout_mode = 2
theme_override_font_sizes/font_size = 5
disabled = true
text = "弃牌"

[node name="NewGameButton" type="Button" parent="MainContainer/CenterArea/ButtonContainer"]
custom_minimum_size = Vector2(30, 12)
layout_mode = 2
theme_override_font_sizes/font_size = 5
text = "新游戏"

[node name="PlayerArea" type="VBoxContainer" parent="MainContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_constants/separation = 1

[node name="HoleCardsContainer" type="HBoxContainer" parent="MainContainer/PlayerArea"]
layout_mode = 2
size_flags_vertical = 3
theme_override_constants/separation = 1
alignment = 1

[node name="PlayerLabel" type="Label" parent="MainContainer/PlayerArea"]
layout_mode = 2
theme_override_font_sizes/font_size = 6
text = "玩家"
horizontal_alignment = 1

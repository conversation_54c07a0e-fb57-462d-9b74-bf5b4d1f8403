class_name ShengJiUI
extends BaseGameUI

# 争上游UI组件
@onready var player_hand_container: HBoxContainer = $MainContainer/PlayerArea/HandContainer
@onready var ai1_info_label: Label = $MainContainer/AI1Area/InfoLabel
@onready var ai2_info_label: Label = $MainContainer/AI2Area/InfoLabel
@onready var last_played_container: HBoxContainer = $MainContainer/CenterArea/LastPlayedContainer
@onready var play_button: Button = $MainContainer/CenterArea/ButtonContainer/PlayButton
@onready var pass_button: Button = $MainContainer/CenterArea/ButtonContainer/PassButton
@onready var new_game_button: Button = $MainContainer/CenterArea/ButtonContainer/NewGameButton

# 卡牌UI数组
var player_hand_uis: Array[PokerCardUI] = []
var last_played_uis: Array[PokerCardUI] = []
var selected_cards: Array[PokerCard] = []

func _ready():
	super._ready()
	
	# 设置组件引用
	game_status_label = $MainContainer/CenterArea/GameStatusLabel
	main_container = $MainContainer
	button_container = $MainContainer/CenterArea/ButtonContainer

func initialize_ui():
	# 连接按钮信号
	if play_button:
		play_button.pressed.connect(_on_play_button_pressed)
	if pass_button:
		pass_button.pressed.connect(_on_pass_button_pressed)
	if new_game_button:
		new_game_button.pressed.connect(_on_new_game_button_pressed)
	
	# 初始化状态
	update_status("点击新游戏开始争上游")
	update_ui_state()

func connect_game_signals():
	super.connect_game_signals()
	
	# 连接争上游特有信号
	if game_instance is ShengJiGame:
		var shengji_game = game_instance as ShengJiGame
		shengji_game.cards_played.connect(_on_cards_played)
		shengji_game.turn_passed.connect(_on_turn_passed)
		shengji_game.round_ended.connect(_on_round_ended)

func _on_card_dealt(card: PokerCard, target: String):
	if target == "玩家":
		var card_ui = create_card_ui(card)
		add_card_ui_to_container(card_ui, player_hand_container)
		player_hand_uis.append(card_ui)
		
		# 连接卡牌点击信号
		card_ui.card_clicked.connect(_on_card_clicked)
		
		play_deal_animation(card_ui, player_hand_uis.size() * 0.1)

func _on_cards_played(player_name: String, cards: Array[PokerCard]):
	# 显示最后出的牌
	clear_last_played()
	
	for card in cards:
		var card_ui = create_card_ui(card)
		add_card_ui_to_container(card_ui, last_played_container)
		last_played_uis.append(card_ui)
	
	show_message(player_name + " 出牌", 1.0)
	update_ui_state()

func _on_turn_passed(player_name: String):
	show_message(player_name + " 过牌", 1.0)

func _on_round_ended(winner: String):
	show_message(winner + " 获胜！", 2.0)

func _on_card_clicked(card_ui: PokerCardUI):
	var card = card_ui.get_card()
	
	if card in selected_cards:
		# 取消选择
		selected_cards.erase(card)
		card_ui.set_selected(false)
	else:
		# 选择卡牌
		selected_cards.append(card)
		card_ui.set_selected(true)
	
	update_ui_state()

func _on_play_button_pressed():
	if game_instance is ShengJiGame and not selected_cards.is_empty():
		var shengji_game = game_instance as ShengJiGame
		if shengji_game.player_play_cards(selected_cards):
			# 出牌成功，从手牌UI中移除
			remove_played_cards()
			selected_cards.clear()

func _on_pass_button_pressed():
	if game_instance is ShengJiGame:
		var shengji_game = game_instance as ShengJiGame
		shengji_game.player_pass()

func _on_new_game_button_pressed():
	if game_instance:
		game_instance.start_new_game()

func remove_played_cards():
	# 移除已出的牌的UI
	for card in selected_cards:
		for i in range(player_hand_uis.size() - 1, -1, -1):
			var card_ui = player_hand_uis[i]
			if card_ui.get_card() == card:
				player_hand_uis.remove_at(i)
				card_ui.queue_free()
				break

func clear_last_played():
	for card_ui in last_played_uis:
		if is_instance_valid(card_ui):
			card_ui.queue_free()
	last_played_uis.clear()

func update_ui_state():
	if not game_instance is ShengJiGame:
		return
	
	var shengji_game = game_instance as ShengJiGame
	var state = shengji_game.get_current_state()
	
	# 更新按钮状态
	var is_player_turn = (shengji_game.current_player_index == 0)
	var is_playing = (state == BaseGame.GameState.PLAYING)
	
	if play_button:
		play_button.disabled = not (is_playing and is_player_turn and not selected_cards.is_empty())
	if pass_button:
		pass_button.disabled = not (is_playing and is_player_turn)
	if new_game_button:
		new_game_button.disabled = (state == BaseGame.GameState.DEALING)
	
	# 更新AI信息
	update_ai_info()

func update_ai_info():
	if not game_instance is ShengJiGame:
		return
	
	var shengji_game = game_instance as ShengJiGame
	
	if ai1_info_label:
		var ai1_count = shengji_game.player_hands[1].size()
		ai1_info_label.text = "AI1: %d张" % ai1_count
	
	if ai2_info_label:
		var ai2_count = shengji_game.player_hands[2].size()
		ai2_info_label.text = "AI2: %d张" % ai2_count

func clear_all_cards():
	super.clear_all_cards()
	
	# 清空玩家手牌
	for card_ui in player_hand_uis:
		if is_instance_valid(card_ui):
			card_ui.queue_free()
	player_hand_uis.clear()
	
	# 清空最后出的牌
	clear_last_played()
	
	# 清空选择
	selected_cards.clear()

func refresh_ui():
	update_ui_state()
	update_ai_info()

class_name MemoryGame
extends BaseGame

# 记忆翻牌特有信号
signal card_flipped(card: PokerCard, position: int)
signal pair_matched(card1: PokerCard, card2: PokerCard)
signal pair_failed(card1: PokerCard, card2: PokerCard)
signal game_completed(moves: int, time: float)

# 游戏状态
var board_cards: Array = []           # 游戏板上的牌
var flipped_cards: Array[int] = []    # 当前翻开的牌位置
var matched_pairs: Array[int] = []    # 已配对的牌位置
var board_size: int = 16              # 游戏板大小（4x4）
var moves_count: int = 0              # 移动次数
var start_time: float = 0             # 开始时间
var is_checking: bool = false         # 是否在检查配对

func _init():
    super._init()
    # 添加玩家
    add_player("玩家")

# 获取游戏名称
func get_game_name() -> String:
    return "记忆翻牌"

# 获取游戏规则
func get_game_rules() -> String:
    return "翻开两张相同的牌进行配对。记住牌的位置，用最少的步数完成所有配对。"

# 开始新游戏
func start_new_game():
    super.start_new_game()
    
    # 重置游戏状态
    reset_game_state()
    
    # 创建游戏板
    create_board()
    
    # 开始游戏
    set_state(GameState.PLAYING)
    start_time = Time.get_time_dict_from_system()["unix"]
    turn_changed.emit("开始翻牌配对")

# 重置游戏状态
func reset_game_state():
    board_cards.clear()
    flipped_cards.clear()
    matched_pairs.clear()
    moves_count = 0
    start_time = 0
    is_checking = false
    
    for player in players:
        player.hand.clear()

# 创建游戏板
func create_board():
    # 创建8对牌（16张）
    var card_pairs = []
    
    # 使用前8种牌面创建对子
    var suits = [PokerCard.Suit.HEARTS, PokerCard.Suit.DIAMONDS]
    var ranks = [
        PokerCard.Rank.ACE, PokerCard.Rank.TWO, PokerCard.Rank.THREE, PokerCard.Rank.FOUR,
        PokerCard.Rank.FIVE, PokerCard.Rank.SIX, PokerCard.Rank.SEVEN, PokerCard.Rank.EIGHT
    ]
    
    # 创建8对牌
    for i in range(8):
        var rank = ranks[i]
        var suit1 = suits[0]
        var suit2 = suits[1]
        
        var card1 = PokerCard.new(suit1, rank)
        var card2 = PokerCard.new(suit2, rank)
        
        card1.is_face_up = false
        card2.is_face_up = false
        
        card_pairs.append(card1)
        card_pairs.append(card2)
    
    # 洗牌
    shuffle_array(card_pairs)
    
    # 放置到游戏板
    board_cards = card_pairs
    
    # 发牌信号（用于UI显示）
    for i in range(board_cards.size()):
        card_dealt.emit(board_cards[i], "位置" + str(i))

# 洗牌数组
func shuffle_array(array: Array):
    for i in range(array.size()):
        var j = randi() % array.size()
        var temp = array[i]
        array[i] = array[j]
        array[j] = temp

# 玩家翻牌
func player_flip_card(position: int) -> bool:
    if current_state != GameState.PLAYING or is_checking:
        return false
    
    # 检查位置是否有效
    if position < 0 or position >= board_cards.size():
        return false
    
    # 检查牌是否已经翻开或已配对
    if position in flipped_cards or position in matched_pairs:
        return false
    
    # 检查是否已经翻开了2张牌
    if flipped_cards.size() >= 2:
        return false
    
    # 翻开牌
    var card = board_cards[position]
    card.is_face_up = true
    flipped_cards.append(position)
    
    card_flipped.emit(card, position)
    
    # 如果翻开了2张牌，检查是否配对
    if flipped_cards.size() == 2:
        moves_count += 1
        is_checking = true
        
        # 立即检查配对，延迟由UI处理
        check_pair()
    
    return true

# 检查配对
func check_pair():
    if flipped_cards.size() != 2:
        return
    
    var pos1 = flipped_cards[0]
    var pos2 = flipped_cards[1]
    var card1 = board_cards[pos1]
    var card2 = board_cards[pos2]
    
    # 检查是否为同一牌面
    if card1.rank == card2.rank:
        # 配对成功
        matched_pairs.append(pos1)
        matched_pairs.append(pos2)
        pair_matched.emit(card1, card2)
        
        # 检查是否完成所有配对
        if matched_pairs.size() >= board_cards.size():
            complete_game()
    else:
        # 配对失败，翻回背面
        card1.is_face_up = false
        card2.is_face_up = false
        pair_failed.emit(card1, card2)
    
    # 清空翻开的牌
    flipped_cards.clear()
    is_checking = false

# 完成游戏
func complete_game():
    var end_time = Time.get_time_dict_from_system()["unix"]
    var game_time = end_time - start_time
    
    game_completed.emit(moves_count, game_time)
    
    var result = {
        "winner": "玩家",
        "moves": moves_count,
        "time": game_time,
        "reason": "完成所有配对"
    }
    
    end_game(result)

# 获取游戏板牌
func get_board_cards() -> Array:
    return board_cards

# 获取游戏板大小
func get_board_size() -> int:
    return board_size

# 获取移动次数
func get_moves_count() -> int:
    return moves_count

# 获取已配对的位置
func get_matched_pairs() -> Array[int]:
    return matched_pairs

# 获取当前翻开的位置
func get_flipped_cards() -> Array[int]:
    return flipped_cards

# 获取游戏时间
func get_game_time() -> float:
    if start_time == 0:
        return 0
    
    var current_time = Time.get_time_dict_from_system()["unix"]
    return current_time - start_time

# 检查位置是否可点击
func is_position_clickable(position: int) -> bool:
    if position < 0 or position >= board_cards.size():
        return false
    
    if position in matched_pairs:
        return false
    
    if position in flipped_cards:
        return false
    
    if flipped_cards.size() >= 2:
        return false
    
    if is_checking:
        return false
    
    return true

# 获取指定位置的牌
func get_card_at_position(position: int) -> PokerCard:
    if position >= 0 and position < board_cards.size():
        return board_cards[position]
    return null

# 重新开始游戏
func restart_game():
    start_new_game()

# 提示功能（显示一对可配对的牌）
func show_hint() -> Array[int]:
    var hint_positions: Array[int] = []
    
    # 找到第一对未配对的牌
    for i in range(board_cards.size()):
        if i in matched_pairs:
            continue
        
        var card1 = board_cards[i]
        for j in range(i + 1, board_cards.size()):
            if j in matched_pairs:
                continue
            
            var card2 = board_cards[j]
            if card1.rank == card2.rank:
                hint_positions = [i, j]
                return hint_positions
    
    return hint_positions

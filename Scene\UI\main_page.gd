extends CanvasLayer

# UI组件引用
@onready var game_selector: GameSelector = $GameSelector
@onready var current_game_ui: Control = null
@onready var game_type_button: Button = $GameTypeButton

# 游戏管理器
var game_manager: GameManager
var current_game_instance: BaseGame = null

# 游戏UI场景
var blackjack_ui_scene: PackedScene = preload("res://Scene/UI/BlackjackUI.tscn")
var texas_holdem_ui_scene: PackedScene = preload("res://Scene/UI/TexasHoldemUI.tscn")
var sheng_ji_ui_scene: PackedScene = preload("res://Scene/UI/ShengJiUI.tscn")
var dou_di_zhu_ui_scene: PackedScene = preload("res://Scene/UI/DouDiZhuUI.tscn")

func _ready() -> void:
    # 初始化游戏管理器
    game_manager = GameManager.new()

    # 连接游戏选择器信号
    if game_selector:
        game_selector.game_selected.connect(_on_game_selected)
        game_selector.selector_closed.connect(_on_selector_closed)
        game_selector.hide()  # 初始隐藏

    # 创建游戏类型选择按钮
    create_game_type_button()

    # 默认加载21点游戏
    switch_to_game(GameManager.GameType.BLACKJACK)

func create_game_type_button():
    # 创建游戏类型选择按钮
    game_type_button = Button.new()
    game_type_button.text = "选择游戏"
    game_type_button.custom_minimum_size = Vector2(80, 25)
    game_type_button.add_theme_font_size_override("font_size", 10)

    # 设置按钮样式，使其更显眼
    game_type_button.add_theme_color_override("font_color", Color.WHITE)
    game_type_button.add_theme_color_override("font_color_hover", Color.YELLOW)
    game_type_button.add_theme_color_override("font_color_pressed", Color.CYAN)

    # 设置按钮位置（右上角，更靠近边缘）
    game_type_button.set_anchors_and_offsets_preset(Control.PRESET_TOP_RIGHT)
    game_type_button.position = Vector2(-85, 2)

    # 连接按钮信号
    game_type_button.pressed.connect(_on_game_type_button_pressed)

    # 添加到场景
    add_child(game_type_button)

    print("游戏选择按钮已创建，位置：", game_type_button.position, "，尺寸：", game_type_button.size)

func _on_game_type_button_pressed():
    if game_selector:
        game_selector.show_selector()

func _on_game_selected(game_type: GameManager.GameType):
    switch_to_game(game_type)

func _on_selector_closed():
    # 游戏选择器关闭后的处理
    pass

# 处理键盘输入
func _input(event):
    if event is InputEventKey and event.pressed:
        # 按Tab键打开游戏选择器
        if event.keycode == KEY_TAB:
            if game_selector and not game_selector.visible:
                game_selector.show_selector()
                get_viewport().set_input_as_handled()
        # 数字键快速切换游戏
        elif event.keycode == KEY_1:
            switch_to_game(GameManager.GameType.BLACKJACK)
            get_viewport().set_input_as_handled()
        elif event.keycode == KEY_2:
            switch_to_game(GameManager.GameType.TEXAS_HOLDEM)
            get_viewport().set_input_as_handled()
        elif event.keycode == KEY_3:
            switch_to_game(GameManager.GameType.SHENG_JI)
            get_viewport().set_input_as_handled()
        elif event.keycode == KEY_4:
            switch_to_game(GameManager.GameType.DOU_DI_ZHU)
            get_viewport().set_input_as_handled()

# 切换到指定游戏
func switch_to_game(game_type: GameManager.GameType):
    # 清理当前游戏
    cleanup_current_game()

    # 创建新游戏实例
    current_game_instance = game_manager.create_game_instance(game_type)
    if not current_game_instance:
        print("错误：无法创建游戏实例")
        return

    # 创建对应的UI
    current_game_ui = create_game_ui(game_type)
    if not current_game_ui:
        print("错误：无法创建游戏UI")
        return

    # 设置游戏UI
    setup_game_ui()

    # 连接游戏实例到UI
    if current_game_ui is BaseGameUI:
        var base_ui = current_game_ui as BaseGameUI
        base_ui.set_game_instance(current_game_instance)

    # 更新游戏管理器
    game_manager.switch_game_type(game_type)
    game_manager.set_current_game(current_game_instance)

    # 更新按钮文本
    if game_type_button:
        game_type_button.text = game_manager.get_game_type_name(game_type)

    print("已切换到游戏：", game_manager.get_game_type_name(game_type))

# 创建游戏UI
func create_game_ui(game_type: GameManager.GameType) -> Control:
    var ui_scene: PackedScene = null

    match game_type:
        GameManager.GameType.BLACKJACK:
            ui_scene = blackjack_ui_scene
        GameManager.GameType.TEXAS_HOLDEM:
            ui_scene = texas_holdem_ui_scene
        GameManager.GameType.SHENG_JI:
            ui_scene = sheng_ji_ui_scene
        GameManager.GameType.DOU_DI_ZHU:
            ui_scene = dou_di_zhu_ui_scene

    if ui_scene:
        return ui_scene.instantiate()
    else:
        print("警告：未找到游戏UI场景")
        return null

# 设置游戏UI
func setup_game_ui():
    if not current_game_ui:
        return

    # 添加到场景
    add_child(current_game_ui)

    # 设置UI填满整个底栏区域
    current_game_ui.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)

    # 设置最小尺寸（适配50像素高度的底栏）
    current_game_ui.custom_minimum_size = Vector2(0, 50)

    print("游戏UI设置完成，尺寸：", current_game_ui.size)

# 清理当前游戏
func cleanup_current_game():
    # 清理当前游戏UI
    if current_game_ui and is_instance_valid(current_game_ui):
        current_game_ui.queue_free()
        current_game_ui = null

    # 清理当前游戏实例
    if current_game_instance:
        current_game_instance = null

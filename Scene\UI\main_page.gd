extends CanvasLayer

# 扑克游戏界面引用
@onready var poker_game_ui: Control = $PokerGameUI

func _ready() -> void:
    # 初始化扑克游戏界面
    if poker_game_ui:
        print("扑克游戏界面已加载")
        # 设置扑克游戏界面适配底栏尺寸
        setup_poker_game_ui()
    else:
        print("错误：无法找到扑克游戏界面")

func setup_poker_game_ui():
    # 确保扑克游戏界面填满整个底栏区域
    poker_game_ui.set_anchors_and_offsets_preset(Control.PRESET_FULL_RECT)
    
    # 设置扑克游戏区域的最小尺寸（适配50像素高度的底栏）
    poker_game_ui.custom_minimum_size = Vector2(0, 50)
    
    print("扑克游戏界面设置完成，尺寸：", poker_game_ui.size)

class_name MemoryGameUI
extends BaseGameUI

# 记忆翻牌UI组件
@onready var game_board_container: GridContainer = $MainContainer/GameBoardContainer
@onready var stats_container: VBoxContainer = $MainContainer/StatsContainer
@onready var moves_label: Label = $MainContainer/StatsContainer/MovesLabel
@onready var time_label: Label = $MainContainer/StatsContainer/TimeLabel
@onready var hint_button: Button = $MainContainer/StatsContainer/HintButton
@onready var restart_button: Button = $MainContainer/StatsContainer/RestartButton

# 卡牌UI数组
var card_buttons: Array[Button] = []
var is_checking_pair: bool = false

func _ready():
	super._ready()
	
	# 设置组件引用
	game_status_label = $MainContainer/GameStatusLabel
	main_container = $MainContainer
	button_container = $MainContainer/StatsContainer

func initialize_ui():
	# 连接按钮信号
	if hint_button:
		hint_button.pressed.connect(_on_hint_button_pressed)
	if restart_button:
		restart_button.pressed.connect(_on_restart_button_pressed)
	
	# 初始化状态
	update_status("点击新游戏开始记忆翻牌")

func connect_game_signals():
	super.connect_game_signals()
	
	# 连接记忆翻牌特有信号
	if game_instance is MemoryGame:
		var memory_game = game_instance as MemoryGame
		memory_game.card_flipped.connect(_on_card_flipped)
		memory_game.pair_matched.connect(_on_pair_matched)
		memory_game.pair_failed.connect(_on_pair_failed)
		memory_game.game_completed.connect(_on_game_completed)

func _on_game_started():
	super._on_game_started()
	create_game_board()
	update_stats()

func create_game_board():
	if not game_instance is MemoryGame:
		return
	
	var memory_game = game_instance as MemoryGame
	var board_cards = memory_game.get_board_cards()
	
	# 清空现有按钮
	for button in card_buttons:
		if is_instance_valid(button):
			button.queue_free()
	card_buttons.clear()
	
	# 设置网格容器为4x4
	if game_board_container:
		game_board_container.columns = 4
		
		# 创建16个卡牌按钮
		for i in range(16):
			var button = Button.new()
			button.custom_minimum_size = Vector2(30, 40)
			button.text = "?"
			button.add_theme_font_size_override("font_size", 8)
			button.pressed.connect(_on_card_button_pressed.bind(i))
			
			game_board_container.add_child(button)
			card_buttons.append(button)

func _on_card_button_pressed(position: int):
	if is_checking_pair:
		return
	
	if game_instance is MemoryGame:
		var memory_game = game_instance as MemoryGame
		if memory_game.is_position_clickable(position):
			memory_game.player_flip_card(position)

func _on_card_flipped(card: PokerCard, position: int):
	# 更新按钮显示
	if position >= 0 and position < card_buttons.size():
		var button = card_buttons[position]
		button.text = get_card_display_text(card)
		button.disabled = true
	
	update_stats()

func _on_pair_matched(card1: PokerCard, card2: PokerCard):
	show_message("配对成功！", 1.0)
	update_stats()

func _on_pair_failed(card1: PokerCard, card2: PokerCard):
	is_checking_pair = true
	show_message("配对失败", 1.0)
	
	# 延迟翻回背面
	await get_tree().create_timer(1.5).timeout
	
	if game_instance is MemoryGame:
		var memory_game = game_instance as MemoryGame
		var flipped_positions = memory_game.get_flipped_cards()
		
		# 翻回背面
		for pos in flipped_positions:
			if pos >= 0 and pos < card_buttons.size():
				var button = card_buttons[pos]
				button.text = "?"
				button.disabled = false
	
	is_checking_pair = false
	update_stats()

func _on_game_completed(moves: int, time: float):
	var minutes = int(time) / 60
	var seconds = int(time) % 60
	var message = "恭喜完成！\n用时：%d分%d秒\n步数：%d步" % [minutes, seconds, moves]
	show_message(message, 5.0)

func _on_hint_button_pressed():
	if game_instance is MemoryGame:
		var memory_game = game_instance as MemoryGame
		var hint_positions = memory_game.show_hint()
		
		if not hint_positions.is_empty():
			# 高亮提示位置
			for pos in hint_positions:
				if pos >= 0 and pos < card_buttons.size():
					var button = card_buttons[pos]
					button.modulate = Color.YELLOW
			
			# 延迟恢复颜色
			await get_tree().create_timer(2.0).timeout
			
			for pos in hint_positions:
				if pos >= 0 and pos < card_buttons.size():
					var button = card_buttons[pos]
					button.modulate = Color.WHITE

func _on_restart_button_pressed():
	if game_instance:
		game_instance.start_new_game()

func update_stats():
	if not game_instance is MemoryGame:
		return
	
	var memory_game = game_instance as MemoryGame
	
	# 更新移动次数
	if moves_label:
		moves_label.text = "步数: %d" % memory_game.get_moves_count()
	
	# 更新时间
	if time_label:
		var game_time = memory_game.get_game_time()
		var minutes = int(game_time) / 60
		var seconds = int(game_time) % 60
		time_label.text = "时间: %d:%02d" % [minutes, seconds]

func get_card_display_text(card: PokerCard) -> String:
	var rank_text = ""
	match card.rank:
		PokerCard.Rank.ACE:
			rank_text = "A"
		PokerCard.Rank.TWO:
			rank_text = "2"
		PokerCard.Rank.THREE:
			rank_text = "3"
		PokerCard.Rank.FOUR:
			rank_text = "4"
		PokerCard.Rank.FIVE:
			rank_text = "5"
		PokerCard.Rank.SIX:
			rank_text = "6"
		PokerCard.Rank.SEVEN:
			rank_text = "7"
		PokerCard.Rank.EIGHT:
			rank_text = "8"
		PokerCard.Rank.NINE:
			rank_text = "9"
		PokerCard.Rank.TEN:
			rank_text = "10"
		PokerCard.Rank.JACK:
			rank_text = "J"
		PokerCard.Rank.QUEEN:
			rank_text = "Q"
		PokerCard.Rank.KING:
			rank_text = "K"
	
	var suit_text = ""
	match card.suit:
		PokerCard.Suit.HEARTS:
			suit_text = "♥"
		PokerCard.Suit.DIAMONDS:
			suit_text = "♦"
		PokerCard.Suit.CLUBS:
			suit_text = "♣"
		PokerCard.Suit.SPADES:
			suit_text = "♠"
	
	return rank_text + suit_text

func clear_all_cards():
	super.clear_all_cards()
	
	# 清空卡牌按钮
	for button in card_buttons:
		if is_instance_valid(button):
			button.queue_free()
	card_buttons.clear()

func refresh_ui():
	update_stats()

# 定时更新时间显示
func _process(_delta):
	if game_instance is MemoryGame:
		var memory_game = game_instance as MemoryGame
		if memory_game.get_current_state() == BaseGame.GameState.PLAYING:
			update_stats()

class_name <PERSON>Game
extends RefCounted

signal game_started()
signal game_ended(winner: String, player_score: int, dealer_score: int)
signal card_dealt(card: PokerCard, is_player: bool)
signal turn_changed(is_player_turn: bool)

# 游戏状态枚举
enum GameState {
	WAITING,    # 等待开始
	DEALING,    # 发牌中
	PLAYER_TURN,# 玩家回合
	DEALER_TURN,# 庄家回合
	GAME_OVER   # 游戏结束
}

var deck: Array[PokerCard] = []
var player_hand: Array[PokerCard] = []
var dealer_hand: Array[PokerCard] = []
var current_state: GameState = GameState.WAITING

# 简化的21点游戏规则
var target_score: int = 21
var dealer_hit_limit: int = 17

func _init():
	initialize_deck()

# 初始化牌堆
func initialize_deck():
	deck.clear()
	
	# 创建标准52张牌（不包含大小王）
	for suit in PokerCard.Suit.values():
		for rank in range(PokerCard.Rank.ACE, PokerCard.Rank.KING + 1):
			var card = PokerCard.new(suit, rank)
			deck.append(card)
	
	shuffle_deck()

# 洗牌
func shuffle_deck():
	for i in range(deck.size()):
		var j = randi() % deck.size()
		var temp = deck[i]
		deck[i] = deck[j]
		deck[j] = temp

# 开始新游戏
func start_new_game():
	# 重置游戏状态
	player_hand.clear()
	dealer_hand.clear()
	current_state = GameState.DEALING
	
	# 如果牌不够，重新洗牌
	if deck.size() < 10:
		initialize_deck()
	
	game_started.emit()
	
	# 发初始牌（玩家2张，庄家2张，庄家第二张背面）
	deal_card_to_player()
	deal_card_to_dealer(true)  # 庄家第一张正面
	deal_card_to_player()
	deal_card_to_dealer(false) # 庄家第二张背面
	
	current_state = GameState.PLAYER_TURN
	turn_changed.emit(true)

# 给玩家发牌
func deal_card_to_player() -> PokerCard:
	if deck.is_empty():
		return null
	
	var card = deck.pop_back()
	card.is_face_up = true
	player_hand.append(card)
	card_dealt.emit(card, true)
	return card

# 给庄家发牌
func deal_card_to_dealer(face_up: bool = true) -> PokerCard:
	if deck.is_empty():
		return null
	
	var card = deck.pop_back()
	card.is_face_up = face_up
	dealer_hand.append(card)
	card_dealt.emit(card, false)
	return card

# 玩家要牌
func player_hit():
	if current_state != GameState.PLAYER_TURN:
		return
	
	deal_card_to_player()
	
	# 检查是否爆牌
	var player_score = calculate_hand_value(player_hand)
	if player_score > target_score:
		end_game()

# 玩家停牌
func player_stand():
	if current_state != GameState.PLAYER_TURN:
		return
	
	current_state = GameState.DEALER_TURN
	turn_changed.emit(false)
	
	# 翻开庄家的背面牌
	for card in dealer_hand:
		card.is_face_up = true
	
	# 庄家自动要牌
	dealer_play()

# 庄家自动游戏
func dealer_play():
	while calculate_hand_value(dealer_hand) < dealer_hit_limit:
		deal_card_to_dealer(true)
		# 注意：延迟需要在UI层处理，这里只是逻辑

	end_game()

# 计算手牌价值
func calculate_hand_value(hand: Array[PokerCard]) -> int:
	var total = 0
	var aces = 0
	
	for card in hand:
		if card.rank == PokerCard.Rank.ACE:
			aces += 1
			total += 11  # 先按11计算
		elif card.rank >= PokerCard.Rank.JACK:
			total += 10  # J, Q, K都算10
		else:
			total += card.rank
	
	# 处理A的特殊情况（1或11）
	while total > target_score and aces > 0:
		total -= 10  # 将A从11改为1
		aces -= 1
	
	return total

# 结束游戏
func end_game():
	current_state = GameState.GAME_OVER
	
	var player_score = calculate_hand_value(player_hand)
	var dealer_score = calculate_hand_value(dealer_hand)
	
	var winner = ""
	if player_score > target_score:
		winner = "庄家"  # 玩家爆牌
	elif dealer_score > target_score:
		winner = "玩家"  # 庄家爆牌
	elif player_score > dealer_score:
		winner = "玩家"
	elif dealer_score > player_score:
		winner = "庄家"
	else:
		winner = "平局"
	
	game_ended.emit(winner, player_score, dealer_score)

# 获取游戏状态
func get_current_state() -> GameState:
	return current_state

# 获取玩家手牌
func get_player_hand() -> Array[PokerCard]:
	return player_hand

# 获取庄家手牌
func get_dealer_hand() -> Array[PokerCard]:
	return dealer_hand

# 获取玩家当前分数
func get_player_score() -> int:
	return calculate_hand_value(player_hand)

# 获取庄家当前分数（只计算正面朝上的牌）
func get_dealer_visible_score() -> int:
	var visible_cards: Array[PokerCard] = []
	for card in dealer_hand:
		if card.is_face_up:
			visible_cards.append(card)
	return calculate_hand_value(visible_cards)

# 检查是否可以要牌
func can_player_hit() -> bool:
	return current_state == GameState.PLAYER_TURN and calculate_hand_value(player_hand) < target_score

# 检查是否可以停牌
func can_player_stand() -> bool:
	return current_state == GameState.PLAYER_TURN

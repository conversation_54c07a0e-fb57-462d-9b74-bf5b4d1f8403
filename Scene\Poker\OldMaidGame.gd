class_name OldMaidGame
extends BaseGame

# 抽乌龟特有信号
signal pairs_removed(player_name: String, pairs: Array)
signal card_drawn(drawer: String, target: String, card: PokerCard)
signal old_maid_revealed(player_name: String)

# 游戏状态
var player_hands: Array[Array] = [[], [], []]  # 3个玩家的手牌
var current_turn: int = 0                      # 当前回合玩家
var old_maid_card: PokerCard = null            # 老K（乌龟牌）
var game_finished_players: Array[int] = []     # 已出完牌的玩家

func _init():
	super._init()
	# 添加3个玩家
	add_player("玩家")
	add_player("AI1")
	add_player("AI2")

# 获取游戏名称
func get_game_name() -> String:
	return "抽乌龟"

# 获取游戏规则
func get_game_rules() -> String:
	return "移除手中的对子，然后轮流从其他玩家手中抽牌。避免最后剩下老K（乌龟）。"

# 开始新游戏
func start_new_game():
	super.start_new_game()
	
	# 重置游戏状态
	reset_game_state()
	
	# 准备特殊牌组（移除一张K作为乌龟）
	prepare_special_deck()
	
	# 发牌
	deal_cards()
	
	# 移除初始对子
	remove_initial_pairs()
	
	# 开始游戏
	current_turn = 0
	set_state(GameState.PLAYING)
	turn_changed.emit("玩家")

# 重置游戏状态
func reset_game_state():
	for i in range(3):
		player_hands[i].clear()
		players[i].hand.clear()
	
	game_finished_players.clear()
	current_turn = 0
	old_maid_card = null

# 准备特殊牌组（移除一张K）
func prepare_special_deck():
	check_deck_size(52)
	
	# 找到并移除一张K作为乌龟牌
	for i in range(deck.size()):
		var card = deck[i]
		if card.rank == PokerCard.Rank.KING:
			old_maid_card = card
			deck.erase(card)
			break
	
	# 重新洗牌
	shuffle_deck()

# 发牌
func deal_cards():
	var total_cards = deck.size()
	var cards_per_player = total_cards / 3
	var extra_cards = total_cards % 3
	
	# 给每个玩家发牌
	for player_index in range(3):
		var cards_to_deal = cards_per_player
		if player_index < extra_cards:
			cards_to_deal += 1
		
		for i in range(cards_to_deal):
			var card = deal_card()
			card.is_face_up = (player_index == 0)  # 只有玩家的牌正面朝上
			player_hands[player_index].append(card)
			players[player_index].hand.append(card)
			card_dealt.emit(card, players[player_index].name)
	
	# 将乌龟牌随机给一个玩家
	var random_player = randi() % 3
	old_maid_card.is_face_up = (random_player == 0)
	player_hands[random_player].append(old_maid_card)
	players[random_player].hand.append(old_maid_card)
	card_dealt.emit(old_maid_card, players[random_player].name)
	
	# 对玩家手牌排序
	sort_hand(player_hands[0])

# 对手牌排序
func sort_hand(hand: Array):
	hand.sort_custom(func(a, b): return a.rank < b.rank)

# 移除初始对子
func remove_initial_pairs():
	for player_index in range(3):
		remove_pairs_from_hand(player_index)

# 从手牌中移除对子
func remove_pairs_from_hand(player_index: int):
	var hand = player_hands[player_index]
	var rank_counts = {}
	var pairs_removed = []
	
	# 统计每种牌面的数量
	for card in hand:
		var rank = card.rank
		if rank in rank_counts:
			rank_counts[rank] += 1
		else:
			rank_counts[rank] = 1
	
	# 移除对子
	for rank in rank_counts:
		var count = rank_counts[rank]
		var pairs_count = count / 2
		
		if pairs_count > 0:
			var cards_to_remove = []
			var found_count = 0
			
			for card in hand:
				if card.rank == rank and found_count < pairs_count * 2:
					cards_to_remove.append(card)
					found_count += 1
			
			# 从手牌中移除
			for card in cards_to_remove:
				hand.erase(card)
				players[player_index].hand.erase(card)
			
			# 记录移除的对子
			for i in range(pairs_count):
				pairs_removed.append([cards_to_remove[i*2], cards_to_remove[i*2+1]])
	
	if not pairs_removed.is_empty():
		pairs_removed.emit(players[player_index].name, pairs_removed)
	
	# 重新排序玩家手牌
	if player_index == 0:
		sort_hand(hand)

# 玩家从指定玩家抽牌
func player_draw_from(target_player_index: int) -> bool:
	if current_turn != 0 or current_state != GameState.PLAYING:
		return false
	
	# 检查目标玩家是否有效且有牌
	if target_player_index < 0 or target_player_index >= 3:
		return false
	
	if target_player_index == 0:
		return false  # 不能从自己抽牌
	
	if target_player_index in game_finished_players:
		return false  # 不能从已完成的玩家抽牌
	
	var target_hand = player_hands[target_player_index]
	if target_hand.is_empty():
		return false
	
	# 随机抽一张牌
	var card_index = randi() % target_hand.size()
	var drawn_card = target_hand[card_index]
	
	# 从目标玩家手中移除
	target_hand.erase(drawn_card)
	players[target_player_index].hand.erase(drawn_card)
	
	# 添加到玩家手中
	drawn_card.is_face_up = true
	player_hands[0].append(drawn_card)
	players[0].hand.append(drawn_card)
	
	card_drawn.emit("玩家", players[target_player_index].name, drawn_card)
	
	# 重新排序
	sort_hand(player_hands[0])
	
	# 移除新形成的对子
	remove_pairs_from_hand(0)
	
	# 检查玩家是否完成
	if player_hands[0].is_empty():
		player_finished(0)
		return true
	
	# 下一个玩家
	next_turn()
	return true

# 下一个玩家回合
func next_turn():
	# 跳过已完成的玩家
	var attempts = 0
	while attempts < 3:
		current_turn = (current_turn + 1) % 3
		if not current_turn in game_finished_players:
			break
		attempts += 1
	
	# AI自动抽牌
	if current_turn != 0:
		ai_draw_card(current_turn)
	else:
		turn_changed.emit("玩家")

# AI抽牌
func ai_draw_card(ai_index: int):
	# 找到可以抽牌的玩家
	var available_targets = []
	for i in range(3):
		if i != ai_index and not i in game_finished_players and not player_hands[i].is_empty():
			available_targets.append(i)
	
	if available_targets.is_empty():
		# 没有可抽牌的目标，游戏结束
		end_game_with_old_maid()
		return
	
	# 随机选择一个目标
	var target_index = available_targets[randi() % available_targets.size()]
	var target_hand = player_hands[target_index]
	
	# 随机抽一张牌
	var card_index = randi() % target_hand.size()
	var drawn_card = target_hand[card_index]
	
	# 从目标玩家手中移除
	target_hand.erase(drawn_card)
	players[target_index].hand.erase(drawn_card)
	
	# 添加到AI手中
	drawn_card.is_face_up = false  # AI的牌背面朝上
	player_hands[ai_index].append(drawn_card)
	players[ai_index].hand.append(drawn_card)
	
	card_drawn.emit(players[ai_index].name, players[target_index].name, drawn_card)
	
	# 移除新形成的对子
	remove_pairs_from_hand(ai_index)
	
	# 检查AI是否完成
	if player_hands[ai_index].is_empty():
		player_finished(ai_index)
		return
	
	# 下一个玩家
	next_turn()

# 玩家完成游戏
func player_finished(player_index: int):
	game_finished_players.append(player_index)
	
	# 检查是否只剩一个玩家
	if game_finished_players.size() >= 2:
		# 找到剩下的玩家（持有乌龟的玩家）
		for i in range(3):
			if not i in game_finished_players:
				old_maid_revealed.emit(players[i].name)
				
				var result = {}
				if i == 0:
					result = {"winner": "AI们", "loser": "玩家", "reason": "玩家持有乌龟"}
				else:
					result = {"winner": "其他玩家", "loser": players[i].name, "reason": players[i].name + "持有乌龟"}
				
				end_game(result)
				return

# 游戏因无法继续而结束
func end_game_with_old_maid():
	# 找到持有乌龟的玩家
	for i in range(3):
		for card in player_hands[i]:
			if card == old_maid_card:
				old_maid_revealed.emit(players[i].name)
				
				var result = {}
				if i == 0:
					result = {"winner": "AI们", "loser": "玩家", "reason": "玩家持有乌龟"}
				else:
					result = {"winner": "其他玩家", "loser": players[i].name, "reason": players[i].name + "持有乌龟"}
				
				end_game(result)
				return

# 获取玩家手牌
func get_player_hand() -> Array:
	return player_hands[0]

# 获取AI手牌数量
func get_ai_hand_count(ai_index: int) -> int:
	if ai_index >= 1 and ai_index <= 2:
		return player_hands[ai_index].size()
	return 0

# 获取可抽牌的目标
func get_available_targets() -> Array[int]:
	var targets: Array[int] = []
	for i in range(1, 3):  # AI1和AI2
		if not i in game_finished_players and not player_hands[i].is_empty():
			targets.append(i)
	return targets

# 获取当前回合玩家
func get_current_turn() -> int:
	return current_turn

# 检查是否持有乌龟
func has_old_maid(player_index: int) -> bool:
	for card in player_hands[player_index]:
		if card == old_maid_card:
			return true
	return false

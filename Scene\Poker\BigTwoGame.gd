class_name BigTwoGame
extends BaseGame

# 锄大地特有信号
signal cards_played(player_name: String, cards: Array)
signal turn_passed(player_name: String)
signal round_ended(winner: String)

# 出牌类型枚举
enum PlayType {
	SINGLE,        # 单张
	PAIR,          # 对子
	TRIPLE,        # 三张
	STRAIGHT,      # 顺子（5张）
	FLUSH,         # 同花（5张）
	FULL_HOUSE,    # 葫芦（3+2）
	FOUR_KIND,     # 四条
	STRAIGHT_FLUSH,# 同花顺
	INVALID        # 无效
}

# 花色等级（锄大地中花色有大小）
enum SuitRank {
	DIAMONDS = 0,  # 方块最小
	CLUBS = 1,     # 梅花
	HEARTS = 2,    # 红心
	SPADES = 3     # 黑桃最大
}

# 游戏状态
var player_hands: Array[Array] = [[], [], [], []]  # 4个玩家的手牌
var last_played_cards: Array = []   # 上次出的牌
var last_played_type: PlayType = PlayType.INVALID
var last_player_index: int = -1
var pass_count: int = 0  # 连续过牌次数
var finished_players: Array[int] = []  # 已出完牌的玩家
var first_turn: bool = true  # 是否第一轮

func _init():
	super._init()
	# 添加4个玩家
	add_player("玩家")
	add_player("AI1")
	add_player("AI2")
	add_player("AI3")

# 获取游戏名称
func get_game_name() -> String:
	return "锄大地"

# 获取游戏规则
func get_game_rules() -> String:
	return "每人13张牌，先出完者获胜。2最大，花色：黑桃>红心>梅花>方块。持有方块3者先出牌。"

# 开始新游戏
func start_new_game():
	super.start_new_game()
	
	# 重置游戏状态
	reset_game_state()
	
	# 发牌
	deal_cards()
	
	# 找到持有方块3的玩家
	var starter = find_diamond_three_holder()
	current_player_index = starter
	first_turn = true
	
	# 开始游戏
	set_state(GameState.PLAYING)
	turn_changed.emit(players[current_player_index].name)

# 重置游戏状态
func reset_game_state():
	for i in range(4):
		player_hands[i].clear()
		players[i].hand.clear()
	
	last_played_cards.clear()
	last_played_type = PlayType.INVALID
	last_player_index = -1
	pass_count = 0
	finished_players.clear()
	current_player_index = 0
	first_turn = true

# 发牌（每人13张）
func deal_cards():
	check_deck_size(52)
	
	# 每人发13张牌
	for round in range(13):
		for player_index in range(4):
			var card = deal_card()
			card.is_face_up = (player_index == 0)  # 只有玩家的牌正面朝上
			player_hands[player_index].append(card)
			players[player_index].hand.append(card)
			card_dealt.emit(card, players[player_index].name)
	
	# 对玩家手牌排序
	sort_hand(player_hands[0])

# 对手牌排序（锄大地排序：3最小，2最大，花色有等级）
func sort_hand(hand: Array):
	hand.sort_custom(func(a, b): return get_card_value(a) < get_card_value(b))

# 获取牌的价值（用于排序和比较）
func get_card_value(card: PokerCard) -> int:
	var rank_value = 0
	
	# 锄大地中的牌面价值：3最小，2最大
	match card.rank:
		PokerCard.Rank.THREE:
			rank_value = 3
		PokerCard.Rank.FOUR:
			rank_value = 4
		PokerCard.Rank.FIVE:
			rank_value = 5
		PokerCard.Rank.SIX:
			rank_value = 6
		PokerCard.Rank.SEVEN:
			rank_value = 7
		PokerCard.Rank.EIGHT:
			rank_value = 8
		PokerCard.Rank.NINE:
			rank_value = 9
		PokerCard.Rank.TEN:
			rank_value = 10
		PokerCard.Rank.JACK:
			rank_value = 11
		PokerCard.Rank.QUEEN:
			rank_value = 12
		PokerCard.Rank.KING:
			rank_value = 13
		PokerCard.Rank.ACE:
			rank_value = 14
		PokerCard.Rank.TWO:
			rank_value = 15  # 2最大
	
	# 花色价值：方块<梅花<红心<黑桃
	var suit_value = 0
	match card.suit:
		PokerCard.Suit.DIAMONDS:
			suit_value = 0
		PokerCard.Suit.CLUBS:
			suit_value = 1
		PokerCard.Suit.HEARTS:
			suit_value = 2
		PokerCard.Suit.SPADES:
			suit_value = 3
	
	# 组合值：牌面价值*10 + 花色价值
	return rank_value * 10 + suit_value

# 找到持有方块3的玩家
func find_diamond_three_holder() -> int:
	for i in range(4):
		for card in player_hands[i]:
			if card.rank == PokerCard.Rank.THREE and card.suit == PokerCard.Suit.DIAMONDS:
				return i
	return 0  # 默认返回玩家

# 玩家出牌
func player_play_cards(selected_cards: Array) -> bool:
	if current_player_index != 0 or current_state != GameState.PLAYING:
		return false
	
	# 第一轮必须包含方块3
	if first_turn and not has_diamond_three(selected_cards):
		return false
	
	# 验证出牌是否有效
	var play_type = get_play_type(selected_cards)
	if not is_valid_play(selected_cards, play_type):
		return false
	
	# 从手牌中移除出的牌
	for card in selected_cards:
		player_hands[0].erase(card)
		players[0].hand.erase(card)
	
	# 更新游戏状态
	last_played_cards = selected_cards.duplicate()
	last_played_type = play_type
	last_player_index = 0
	pass_count = 0
	first_turn = false
	
	cards_played.emit("玩家", selected_cards)
	
	# 检查是否出完牌
	if player_hands[0].is_empty():
		player_finished(0)
		return true
	
	# 下一个玩家
	next_turn()
	return true

# 检查是否包含方块3
func has_diamond_three(cards: Array) -> bool:
	for card in cards:
		if card.rank == PokerCard.Rank.THREE and card.suit == PokerCard.Suit.DIAMONDS:
			return true
	return false

# 玩家过牌
func player_pass() -> bool:
	if current_player_index != 0 or current_state != GameState.PLAYING:
		return false
	
	# 第一轮不能过牌
	if first_turn:
		return false
	
	# 如果是第一次出牌，不能过
	if last_played_cards.is_empty():
		return false
	
	pass_count += 1
	turn_passed.emit("玩家")
	
	# 下一个玩家
	next_turn()
	return true

# 下一个玩家回合
func next_turn():
	# 跳过已完成的玩家
	var attempts = 0
	while attempts < 4:
		current_player_index = (current_player_index + 1) % 4
		if not current_player_index in finished_players:
			break
		attempts += 1
	
	# 检查是否所有其他玩家都过牌了
	if pass_count >= 3:
		# 重新开始新一轮
		last_played_cards.clear()
		last_played_type = PlayType.INVALID
		pass_count = 0
	
	# AI自动出牌
	if current_player_index != 0:
		ai_play(current_player_index)
	else:
		turn_changed.emit("玩家")

# AI出牌（简化实现）
func ai_play(ai_index: int):
	var ai_hand = player_hands[ai_index]
	
	if last_played_cards.is_empty() or first_turn:
		# 第一次出牌
		var cards_to_play = []
		if first_turn and ai_index == find_diamond_three_holder():
			# 必须出方块3
			for card in ai_hand:
				if card.rank == PokerCard.Rank.THREE and card.suit == PokerCard.Suit.DIAMONDS:
					cards_to_play = [card]
					break
		else:
			# 出最小的单张
			if not ai_hand.is_empty():
				cards_to_play = [ai_hand[0]]
		
		if not cards_to_play.is_empty():
			play_ai_cards(ai_index, cards_to_play)
		else:
			ai_pass(ai_index)
	else:
		# 尝试跟牌或过牌
		if randi() % 2 == 0:
			ai_pass(ai_index)
		else:
			# 简化：尝试出单张
			var valid_cards = get_valid_single_cards(ai_hand)
			if not valid_cards.is_empty():
				play_ai_cards(ai_index, [valid_cards[0]])
			else:
				ai_pass(ai_index)

# AI出牌
func play_ai_cards(ai_index: int, cards: Array):
	var ai_hand = player_hands[ai_index]
	
	for card in cards:
		ai_hand.erase(card)
		players[ai_index].hand.erase(card)
	
	last_played_cards = cards.duplicate()
	last_played_type = get_play_type(cards)
	last_player_index = ai_index
	pass_count = 0
	first_turn = false
	
	cards_played.emit(players[ai_index].name, cards)
	
	# 检查是否出完牌
	if ai_hand.is_empty():
		player_finished(ai_index)
		return
	
	next_turn()

# AI过牌
func ai_pass(ai_index: int):
	pass_count += 1
	turn_passed.emit(players[ai_index].name)
	next_turn()

# 获取AI可以出的单张牌
func get_valid_single_cards(hand: Array) -> Array:
	var valid_cards: Array = []
	
	if last_played_type == PlayType.SINGLE and not last_played_cards.is_empty():
		var last_value = get_card_value(last_played_cards[0])
		for card in hand:
			if get_card_value(card) > last_value:
				valid_cards.append(card)
	
	return valid_cards

# 玩家完成游戏
func player_finished(player_index: int):
	finished_players.append(player_index)
	
	var result = {}
	if player_index == 0:
		result = {"winner": "玩家", "rank": finished_players.size()}
	else:
		result = {"winner": players[player_index].name, "rank": finished_players.size()}
	
	# 检查游戏是否结束
	if finished_players.size() >= 3:
		# 游戏结束，确定最终排名
		var final_result = {"rankings": []}
		for i in range(finished_players.size()):
			final_result.rankings.append({
				"player": players[finished_players[i]].name,
				"rank": i + 1
			})
		
		# 添加未完成的玩家
		for i in range(4):
			if not i in finished_players:
				final_result.rankings.append({
					"player": players[i].name,
					"rank": finished_players.size() + 1
				})
		
		end_game(final_result)
	else:
		round_ended.emit(players[player_index].name)

# 获取出牌类型
func get_play_type(cards: Array) -> PlayType:
	if cards.is_empty():
		return PlayType.INVALID
	
	var count = cards.size()
	
	match count:
		1:
			return PlayType.SINGLE
		2:
			if cards[0].rank == cards[1].rank:
				return PlayType.PAIR
			else:
				return PlayType.INVALID
		3:
			if cards[0].rank == cards[1].rank and cards[1].rank == cards[2].rank:
				return PlayType.TRIPLE
			else:
				return PlayType.INVALID
		5:
			# 检查是否为特殊5张牌型
			return check_five_card_type(cards)
		_:
			return PlayType.INVALID

# 检查5张牌的类型
func check_five_card_type(cards: Array) -> PlayType:
	if cards.size() != 5:
		return PlayType.INVALID
	
	var sorted_cards = cards.duplicate()
	sorted_cards.sort_custom(func(a, b): return get_card_value(a) < get_card_value(b))
	
	# 检查同花顺
	if is_straight(sorted_cards) and is_flush(sorted_cards):
		return PlayType.STRAIGHT_FLUSH
	
	# 检查四条
	if is_four_kind(sorted_cards):
		return PlayType.FOUR_KIND
	
	# 检查葫芦
	if is_full_house(sorted_cards):
		return PlayType.FULL_HOUSE
	
	# 检查同花
	if is_flush(sorted_cards):
		return PlayType.FLUSH
	
	# 检查顺子
	if is_straight(sorted_cards):
		return PlayType.STRAIGHT
	
	return PlayType.INVALID

# 检查是否为顺子
func is_straight(cards: Array) -> bool:
	if cards.size() != 5:
		return false
	
	for i in range(1, cards.size()):
		var prev_rank = get_rank_value(cards[i-1].rank)
		var curr_rank = get_rank_value(cards[i].rank)
		if curr_rank != prev_rank + 1:
			return false
	
	return true

# 检查是否为同花
func is_flush(cards: Array) -> bool:
	if cards.size() != 5:
		return false
	
	var suit = cards[0].suit
	for card in cards:
		if card.suit != suit:
			return false
	
	return true

# 检查是否为四条
func is_four_kind(cards: Array) -> bool:
	if cards.size() != 5:
		return false
	
	var ranks = {}
	for card in cards:
		var rank = card.rank
		if rank in ranks:
			ranks[rank] += 1
		else:
			ranks[rank] = 1
	
	for count in ranks.values():
		if count == 4:
			return true
	
	return false

# 检查是否为葫芦
func is_full_house(cards: Array) -> bool:
	if cards.size() != 5:
		return false
	
	var ranks = {}
	for card in cards:
		var rank = card.rank
		if rank in ranks:
			ranks[rank] += 1
		else:
			ranks[rank] = 1
	
	var has_three = false
	var has_pair = false
	
	for count in ranks.values():
		if count == 3:
			has_three = true
		elif count == 2:
			has_pair = true
	
	return has_three and has_pair

# 获取牌面等级值（用于顺子检查）
func get_rank_value(rank: PokerCard.Rank) -> int:
	match rank:
		PokerCard.Rank.THREE:
			return 3
		PokerCard.Rank.FOUR:
			return 4
		PokerCard.Rank.FIVE:
			return 5
		PokerCard.Rank.SIX:
			return 6
		PokerCard.Rank.SEVEN:
			return 7
		PokerCard.Rank.EIGHT:
			return 8
		PokerCard.Rank.NINE:
			return 9
		PokerCard.Rank.TEN:
			return 10
		PokerCard.Rank.JACK:
			return 11
		PokerCard.Rank.QUEEN:
			return 12
		PokerCard.Rank.KING:
			return 13
		PokerCard.Rank.ACE:
			return 14
		PokerCard.Rank.TWO:
			return 15
		_:
			return 0

# 验证出牌是否有效
func is_valid_play(cards: Array, play_type: PlayType) -> bool:
	if play_type == PlayType.INVALID:
		return false
	
	# 如果是第一次出牌，任何有效牌型都可以
	if last_played_cards.is_empty():
		return true
	
	# 必须是相同类型的牌
	if play_type != last_played_type:
		return false
	
	# 比较牌的大小
	return compare_cards(cards, last_played_cards) > 0

# 比较两组牌的大小
func compare_cards(cards1: Array, cards2: Array) -> int:
	if cards1.size() != cards2.size():
		return 0
	
	match cards1.size():
		1:
			return get_card_value(cards1[0]) - get_card_value(cards2[0])
		2, 3:
			return get_card_value(cards1[0]) - get_card_value(cards2[0])
		5:
			# 5张牌比较最高牌
			var max1 = get_card_value(cards1[0])
			var max2 = get_card_value(cards2[0])
			for card in cards1:
				max1 = max(max1, get_card_value(card))
			for card in cards2:
				max2 = max(max2, get_card_value(card))
			return max1 - max2
		_:
			return 0

# 获取玩家手牌
func get_player_hand() -> Array:
	return player_hands[0]

# 获取可选择的牌
func get_selectable_cards() -> Array:
	if current_player_index != 0:
		return []
	
	return player_hands[0]
